<?php

namespace App\Traits;

use App\Exceptions\CustomException;
use App\Exports\ServiceLoadingPortServiceExport;
use App\Jobs\RemoveAttachment;
use App\Models\Billing\BEXP;
use App\Models\Billing\BEXP1;
use App\Models\ExchangeRate\ExchangeRate;
use App\Models\MasterBilling\Classification;
use App\Services\BiCurrencyService;
use App\Services\ConvertDocxToPdfService;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Maatwebsite\Excel\Facades\Excel;
use Madnest\Madzipper\Madzipper as Zipper;
use PDF;
use PhpOffice\PhpWord\TemplateProcessor;

trait BillingHelper
{
    /**
     * @param $portService
     * @param $vessel_arrive
     * @param $tenant
     *
     * @return string
     */
    public function checkPortSeviceExist($portService, $vessel_arrive, $tenant)
    {
        $vessel_arrive = date('Y-m-d', strtotime($vessel_arrive));
        $port_service = substr($portService, 0, 7);
        $ps_tenant = DB::table('MPLPS')
            ->where('TenantKey', '=', $this->getTenantByName($tenant))
            ->where('Type', '=', 'Tenant')
            ->whereRaw("
                '${vessel_arrive}' >= convert(varchar, PeriodStart, 23)
                AND '${vessel_arrive}' <= convert(varchar, PeriodEnd, 23) AND Type='Tenant'
            ")
            ->count();
        if ($ps_tenant > 0) {
            $data_ps = DB::table('MPS AS T1')
                ->leftJoin('MPLPS AS T2', 'T1.DocEntry', 'T2.PSKey')
                ->leftJoin('M_Currency AS T3', 'T2.Currency', 'T3.DocEntry')
                ->whereRaw("
                    '${vessel_arrive}' >= convert(varchar, T2.PeriodStart, 23)
                    AND '${vessel_arrive}' <= convert(varchar, T2.PeriodEnd, 23) AND T2.Type='Tenant'
                ")
                ->selectRaw("
                    CONCAT(T1.ItemCode, ' - ', T1.FrgnName) AS Name
                    ,T2.Price
                    ,T3.Currency
                ")
                ->where('T2.TenantKey', '=', $this->getTenantByName($tenant))
                ->where('T1.Active', '=', 'Y')
                ->where('T1.ItemCode', '=', $port_service)
                ->first();

            if (!$data_ps) {
                return 'tenant';
            }
        } else {
            $data_ps = DB::table('MPS AS T1')
                ->leftJoin('MPLPS AS T2', 'T1.DocEntry', 'T2.PSKey')
                ->leftJoin('M_Currency AS T3', 'T2.Currency', 'T3.DocEntry')
                ->whereRaw("
                '${vessel_arrive}' >= convert(varchar, T2.PeriodStart, 23)
                AND '${vessel_arrive}' <= convert(varchar, T2.PeriodEnd, 23) AND T2.Type='Global'
            ")
                ->selectRaw("
                CONCAT(T1.ItemCode, ' - ', T1.FrgnName) AS Name
                ,T2.Price
                ,T3.Currency
            ")
                ->where('T1.Active', '=', 'Y')
                ->where('T1.ItemCode', '=', $port_service)
                ->first();

            if (!$data_ps) {
                return 'global';
            }
        }
    }

    /**
     * @param $vessel_arrive
     * @param $tenant
     *
     * @return string
     */
    public function checkServiceLoadingExist($vessel_arrive, $tenant)
    {
        $vessel_arrive = date('Y-m-d', strtotime($vessel_arrive));
        $ps_tenant = DB::table('MPLSL')
            ->where('TenantKey', '=', $this->getTenantByName($tenant))
            ->where('Type', '=', 'Tenant')
            ->whereRaw("
                '${vessel_arrive}' >= convert(varchar, PeriodStart, 23)
                AND '${vessel_arrive}' <= convert(varchar, PeriodEnd, 23) AND Type='Tenant'
            ")
            ->count();
        if ($ps_tenant > 0) {
            $data_ps = DB::table('MPLSL AS T2')
                ->leftJoin('M_Currency AS T3', 'T2.Currency', 'T3.DocEntry')
                ->whereRaw("
                    '${vessel_arrive}' >= convert(varchar, T2.PeriodStart, 23)
                    AND '${vessel_arrive}' <= convert(varchar, T2.PeriodEnd, 23) AND T2.Type='Tenant'
                ")
                ->where('TenantKey', '=', $this->getTenantByName($tenant))
                ->selectRaw('
                    T2.Price
                    ,T3.Currency
                ')
                ->first();

            if (!$data_ps) {
                return 'tenant';
            }
        } else {
            $data_ps = DB::table('MPLSL AS T2')
                ->leftJoin('M_Currency AS T3', 'T2.Currency', 'T3.DocEntry')
                ->whereRaw("
                    '${vessel_arrive}' >= convert(varchar, T2.PeriodStart, 23)
                    AND '${vessel_arrive}' <= convert(varchar, T2.PeriodEnd, 23) AND T2.Type='Global'
                ")
                ->selectRaw('
                    T2.Price
                    ,T3.Currency
                ')
                ->first();
            if (!$data_ps) {
                return 'global';
            }
        }
    }

    /**
     * @param $i
     * @param $items
     * @param $request
     * @param $id
     * @param $type
     *
     * @return mixed
     */
    public function storeBilling($i, $items, $request, $id, $type)
    {
        $last_data = key_exists('DocEntry', $items) ? $items['DocEntry'] : null;
        $index = $i;
        $index *= 0;
        $port_service = array_key_exists('PortServiceType', $items) ? substr($items['PortServiceType'], 0, 7) :
            substr($request->form['PortService'], 0, 7);
        $monitor_doc = BEXP::where('DocEntry', '=', $last_data)->first();
        $created = !empty($monitor_doc->created_at) ? $monitor_doc->created_at : Carbon::now();

        if (array_key_exists('BaseIdString', $items)) {
            if ($items['BaseIdString']) {
                $base_id_string = $items['BaseIdString'];
                $count_document = DB::table('BEXP')
                    ->whereRaw("BaseId IN (${base_id_string})")
                    ->where('DocNum', '=', $id)
                    ->count();
                if ($count_document > 0) {
                    DB::table('BEXP')
                        ->whereRaw("BaseId IN (${base_id_string})")
                        ->where('DocNum', '=', $id)
                        ->delete();
                }
            }
        }

        if ($type == 'IN' || $type == 'OUT') {
            $ps_type = 'Local';
        } else {
            $ps_type = $type;
        }

        $ps_total = $this->calcTotal($port_service, $request->form['VesselDeparture'], $items['Tenant'], $ps_type);
        $sl_total = $this->calcTotalServiceLoading($request->form['VesselDeparture'], $items['Tenant']);

        $total_estimate = array_key_exists('TotalEstimate', $items) ? $items['TotalEstimate'] : 0;

        $data_tenant = DB::table('M_Tenant')->where('Name', '=', $items['Tenant'])->first();

        // calculate total nota penagihan
        $qty_estimate = isset($items['QtyEstimate']) ? sprintf('%f', floatval($items['QtyEstimate'])) : 0;
        $qty_revised = isset($items['QtyRevised']) ? floatval($items['QtyRevised']) : 0;
        $sub_total = 0;
        $vat_value = 0;
        $sub_total_sl = 0;
        $vat_value_sl = 0;
        $grand_total_sl = 0;
        $grand_total = 0;
        if ($type == 'IN' || $type == 'OUT') {
            $qty_estimate = ($qty_revised == 0) ? $qty_estimate : $qty_revised;
            if ($qty_estimate) {
                $pricePortService = $this->getPrice($port_service, $request->form['VesselDeparture'], $items['Tenant'], $ps_type);
                $price = isset($items['PriceEstimate']) ? round($pricePortService, 2) : 0;
                // $price = $pricePortService;
                $tax_code = isset($items['TaxCode']) ? $items['TaxCode'] : null;
                $departure = $request->form['BillingNoteDate'];
                $departure = date('Y-m-d', strtotime($departure));
                $rate = ExchangeRate::where('RateDate', '=', $departure)->first();
                if ($rate) {
                    $sub_total = round(round(($qty_estimate * $price), 2) * $rate->IdrPrice);
                    // return response()->json($sub_total, 422);
                    $vat_value = floor($tax_code * $sub_total / 100);
                    $grand_total = $vat_value + $sub_total;
                }

                if ($items['LoadingUnloadingType'] == 'Yes') {
                    $price = floatval(10000);
                    $sub_total_sl = round($qty_estimate * ($price));
                    $vat_value_sl = floor($tax_code * $sub_total_sl / 100);
                    $grand_total_sl = $vat_value_sl + $sub_total_sl;
                }
            }
        }

        $total_estimate = $grand_total;

        $price = $this->getPrice($port_service, $request->form['VesselDeparture'], $items['Tenant'], $ps_type);

        if ($monitor_doc) {
            $monitor_doc->TenantId = $this->getTenantByName($items['Tenant']);
            $monitor_doc->IsTenant = $data_tenant->IsTenant;
            $monitor_doc->VatValue = $vat_value;
            $monitor_doc->SubTotal = $sub_total;
            $monitor_doc->VatValueSL = $vat_value_sl;
            $monitor_doc->SubTotalSL = $sub_total_sl;
            $monitor_doc->TotalEstimateSL = $grand_total_sl;
            $monitor_doc->Notification = $items['Notification'];
            $monitor_doc->NoBL = $items['NoBL'] ? $items['NoBL'] : '';
            $monitor_doc->DateBL = $items['DateBL'] ? $this->toDate($items['DateBL']) : null;
            $monitor_doc->BaseId = $items['BaseId'] ? $items['BaseId'] : null;
            $monitor_doc->BCType = $this->getBcType('3.0');
            $monitor_doc->BP = $items['BP'] ? $this->getDocEntryBP($items['BP']) : 0;
            $monitor_doc->LoadingItem = $items['LoadingItem'];
            $monitor_doc->ItemName = $items['LoadingItem'];
            $monitor_doc->Classification = $this->storeClassification($items, $type);
            $monitor_doc->WeightCategory = $items['WeightCategory'];
            $monitor_doc->LoadingQty = $items['LoadingQty'];
            //$monitor_doc->Unit = $items['Unit'];
            $monitor_doc->CompanyHeader = $items['CompanyHeader'];
            $monitor_doc->NetWeight = (isset($items['NetWeight'])) ? $items['NetWeight'] : 0;
            $monitor_doc->UnitPrice = (isset($items['UnitPrice'])) ? $items['UnitPrice'] : 0;
            $monitor_doc->TotalInv = (isset($items['TotalInv'])) ? $items['TotalInv'] : 0;
            $monitor_doc->QtyEstimate = isset($items['QtyEstimate']) ? sprintf('%f', floatval($items['QtyEstimate'])) : 0;
            // $monitor_doc->PriceEstimate = isset($items['PriceEstimate']) ? round($items['PriceEstimate'], 2) : 0;
            $monitor_doc->PriceEstimate = isset($items['PriceEstimate']) ? round($price, 2) : 0;
            $monitor_doc->QtyRevised = isset($items['QtyRevised']) ? $items['QtyRevised'] : 0;
            $monitor_doc->PriceRevised = isset($items['PriceRevised']) ? $items['PriceRevised'] : 0;
            $monitor_doc->BillingType = isset($items['BillingType']) ? $items['BillingType'] : null;
            $monitor_doc->TotalEstimate = round($total_estimate, 2);
            $monitor_doc->TotalRevised = isset($items['TotalRevised']) ? $items['TotalRevised'] : 0;
            $monitor_doc->TaxCode = isset($items['TaxCode']) ? $items['TaxCode'] : null;
            $monitor_doc->ChargeTo = isset($items['ChargeTo']) ? $items['ChargeTo'] : null;
            $monitor_doc->NoNota = isset($items['NoNota']) ? $items['NoNota'] : $this->generateIvNo(Carbon::now(), $items['CompanyHeader'], $total_estimate, $i, $id, 'PS', $monitor_doc->Type);
            $monitor_doc->DockCleaningNo = isset($items['DockCleaningNo']) ? $items['DockCleaningNo'] : $this->generateIvNo(Carbon::now(), $items['CompanyHeader'], $total_estimate, $i, $id, 'JKD', $monitor_doc->Type);

            if ($items['LoadingUnloadingType'] == 'Yes') {
                $monitor_doc->NoNotaSl = isset($items['NoNotaSl']) ? $items['NoNotaSl'] : $this->generateIvNo(Carbon::now(), $items['CompanyHeader'], $total_estimate, $i, $id, 'SL', $monitor_doc->Type);
            }
            $monitor_doc->PortServiceType = $port_service;
            $monitor_doc->LoadingUnloadingType = $items['LoadingUnloadingType'] ?
                $items['LoadingUnloadingType'] : 'All';
            $monitor_doc->Signature1 = $items['Signature1'];
            $monitor_doc->Signature2 = $items['Signature2'];
            $monitor_doc->Signature3 = $items['Signature3'];
            $monitor_doc->AttachmentText = $items['AttachmentText'];
            // $monitor_doc->Price = $items['Tenant'] != 'BDM' ? $this->getPrice(
            //     $port_service,
            //     $request->form['VesselArrival'],
            //     $items['Tenant'],
            //     $ps_type
            // ) : 0;

            $monitor_doc->Price = array_key_exists('Price', $items) ? ($items['Tenant'] != 'BDM' ? $price : 0) : null;

            $monitor_doc->ServiceLoading = $items['Tenant'] != 'BDM' ? (
                ($items['LoadingUnloadingType'] == 'Yes') ? $this->getPriceServiceLoading(
                    $request->form['VesselDeparture'],
                    $items['Tenant']
                ) : 0) : 0;
            $monitor_doc->Jetty = array_key_exists('Jetty', $items) ?
                $this->getJettyByName($items['Jetty']) : $this->getJettyByName($request->form['Jetty']);
            $monitor_doc->updated_at = Carbon::now();
            $monitor_doc->created_at = $created;
            $monitor_doc->UpdatedBy = $request->user()->id;
            $monitor_doc->Total = $items['Tenant'] != 'BDM' ? round(($ps_total->Price * $items['LoadingQty']), 2) : 0;
            $monitor_doc->CurrencyPortService = $ps_total->Currency;
            $monitor_doc->TotalServiceLoading = $items['Tenant'] != 'BDM' ?
                (($items['LoadingUnloadingType'] == 'Yes') ? round(($sl_total->Price * $items['LoadingQty']), 2) : 0) : 0;
            $monitor_doc->CurrencyServiceLoading = $sl_total->Currency;
            $monitor_doc->Agent = $this->getAgent($items['Agent']) ? $this->getAgent($items['Agent']) : null;
            //$monitor_doc->DocStatus = $items['DocStatus'];
            $monitor_doc->save();
            return $monitor_doc->DocEntry;
        }


        $monitor_doc = new BEXP();
        $monitor_doc->DocNum = $id;
        $monitor_doc->VatValue = $vat_value;
        $monitor_doc->SubTotal = $sub_total;
        $monitor_doc->VatValueSL = $vat_value_sl;
        $monitor_doc->SubTotalSL = $sub_total_sl;
        $monitor_doc->TotalEstimateSL = $grand_total_sl;
        $monitor_doc->TenantId = $this->getTenantByName($items['Tenant']);
        $monitor_doc->IsTenant = $data_tenant->IsTenant;
        $monitor_doc->Notification = $items['Notification'];
        $monitor_doc->LineNum = ($i + 1);
        $monitor_doc->NoBL = array_key_exists('NoBL', $items) ? ($items['NoBL'] ? $items['NoBL'] : '') : '';
        $monitor_doc->DateBL = key_exists('DateBL', $items) ? $this->toDate($items['DateBL']) : null;
        $monitor_doc->BaseId = key_exists('BaseId', $items) ? $items['BaseId'] : null;
        $monitor_doc->BCType = $this->getBcType('3.0');
        $monitor_doc->BP = key_exists('BP', $items) ? $this->getDocEntryBP($items['BP']) : 0;
        $monitor_doc->LoadingItem = $items['LoadingItem'];
        $monitor_doc->ItemName = $items['LoadingItem'];
        $monitor_doc->Classification = $this->storeClassification($items, $type);
        $monitor_doc->WeightCategory = $items['WeightCategory'];
        $monitor_doc->LoadingQty = $items['LoadingQty'];
        //$monitor_doc->Unit = $items['Unit'];
        $monitor_doc->CompanyHeader = $items['CompanyHeader'];
        $monitor_doc->NetWeight = (isset($items['NetWeight'])) ? $items['NetWeight'] : 0;
        $monitor_doc->UnitPrice = (isset($items['UnitPrice'])) ? $items['UnitPrice'] : 0;
        $monitor_doc->TotalInv = (isset($items['TotalInv'])) ? $items['TotalInv'] : 0;
        $monitor_doc->QtyEstimate = $qty_estimate;
        // $monitor_doc->PriceEstimate = isset($items['PriceEstimate']) ? round($items['PriceEstimate'], 2) : 0;
        $monitor_doc->PriceEstimate = isset($items['PriceEstimate']) ? round($price, 2) : 0;
        $monitor_doc->QtyRevised = isset($items['QtyRevised']) ? $items['QtyRevised'] : 0;
        $monitor_doc->PriceRevised = isset($items['PriceRevised']) ? $items['PriceRevised'] : 0;
        $monitor_doc->BillingType = isset($items['BillingType']) ? $items['BillingType'] : null;
        $monitor_doc->ChargeTo = isset($items['ChargeTo']) ? $items['ChargeTo'] : null;
        $monitor_doc->TotalEstimate = round($total_estimate, 2);
        $monitor_doc->TotalRevised = isset($items['TotalRevised']) ? $items['TotalRevised'] : 0;
        $monitor_doc->TaxCode = isset($items['TaxCode']) ? $items['TaxCode'] : null;
        $monitor_doc->PortServiceType = $port_service;
        $monitor_doc->LoadingUnloadingType = array_key_exists('LoadingUnloadingType', $items) ?
            $items['LoadingUnloadingType'] : 'All';
        $monitor_doc->Signature1 = array_key_exists('Signature1', $items) ? $items['Signature1'] : null;
        $monitor_doc->Signature2 = array_key_exists('Signature2', $items) ? $items['Signature2'] : null;
        $monitor_doc->Signature3 = array_key_exists('Signature3', $items) ? $items['Signature3'] : null;
        $monitor_doc->AttachmentText = array_key_exists('AttachmentText', $items)
            ? $items['AttachmentText'] : null;
        $monitor_doc->Price = array_key_exists('Price', $items) ? ($items['Tenant'] != 'BDM' ? $price : 0) : null;
        $monitor_doc->ServiceLoading = $items['Tenant'] != 'BDM' ? (
            ($items['LoadingUnloadingType'] == 'Yes') ? $this->getPriceServiceLoading(
                $request->form['VesselDeparture'],
                $items['Tenant']
            ) : 0) : 0;
        $monitor_doc->Jetty = array_key_exists('Jetty', $items) ?
            $this->getJettyByName($items['Jetty']) : $this->getJettyByName($request->form['Jetty']);
        $monitor_doc->CreatedBy = $request->user()->id;
        $monitor_doc->UpdatedBy = $request->user()->id;
        $monitor_doc->Total = $items['Tenant'] != 'BDM' ? round(($ps_total->Price * $items['LoadingQty']), 2) : 0;
        $monitor_doc->CurrencyPortService = $ps_total->Currency;
        $monitor_doc->TotalServiceLoading = $items['Tenant'] != 'BDM' ?
            (($items['LoadingUnloadingType'] == 'Yes') ? round(($sl_total->Price * $items['LoadingQty']), 2) : 0) : 0;
        $monitor_doc->CurrencyServiceLoading = $sl_total->Currency;
        $monitor_doc->created_at = Carbon::now();
        $monitor_doc->Status = 'Open';
        $monitor_doc->NoNota = $this->generateIvNo(Carbon::now(), $items['CompanyHeader'], $total_estimate, $i, $id, 'PS', $type);
        $monitor_doc->DockCleaningNo = $this->generateIvNo(Carbon::now(), $items['CompanyHeader'], $total_estimate, $i, $id, 'JKD', $type);
        if ($items['LoadingUnloadingType'] == 'Yes') {
            $monitor_doc->NoNotaSl = $this->generateIvNo(Carbon::now(), $items['CompanyHeader'], $total_estimate, $i, $id, 'SL', $type);
        }
        $monitor_doc->Type = $type;
        $monitor_doc->Agent = $this->getAgent($items['Agent']) ? $this->getAgent($items['Agent']) : null;
        $monitor_doc->save();

        return $monitor_doc->DocEntry;
    }

    /**
     * @param $sysDate
     *
     * @return string
     */
    protected function generateIvNo($sysDate, $company_header, $total_estimate, $index, $id, $type, $docType)
    {
        $data_date = strtotime($sysDate);
        $year_val = date('Y', $data_date);
        $month_romawi = date('n', $data_date);
        if (isset($total_estimate) && $total_estimate > 0) {
            $header = DB::table('BHLOCAL')->where('DocEntry', '=', $id)->first();

            return $header->DocNum . '-' . ($index + 1) . '/' . $company_header . "/NP/${type}/" . $month_romawi . '/' . $year_val;
        } elseif ($docType == 'Import') {
            $header = DB::table('BHIMP')->where('DocEntry', '=', $id)->first();
            return $header->DocNum . '-' . ($index + 1) . '/' . $company_header . "/NP/${type}/" . $month_romawi . '/' . $year_val;
        } elseif ($docType == 'Export') {
            $header = DB::table('BHEXP')->where('DocEntry', '=', $id)->first();
            return $header->DocNum . '-' . ($index + 1) . '/' . $company_header . "/NP/${type}/" . $month_romawi . '/' . $year_val;
        } else {
            return null;
        }
    }


    protected function getRomawi($month)
    {
        switch ($month) {
            case 1:
                return "I";
                break;
            case 2:
                return "II";
                break;
            case 3:
                return "III";
                break;
            case 4:
                return "IV";
                break;
            case 5:
                return "V";
                break;
            case 6:
                return "VI";
                break;
            case 7:
                return "VII";
                break;
            case 8:
                return "VIII";
                break;
            case 9:
                return "IX";
                break;
            case 10:
                return "X";
                break;
            case 11:
                return "XI";
                break;
            case 12:
                return "XII";
                break;
        }
    }

    /**
     * @param $portService
     * @param $weight
     * @param $vessel_arrive
     * @param $tenant
     *
     * @return \Illuminate\Database\Eloquent\Model|\Illuminate\Database\Query\Builder|object
     */
    public function calcTotal($portService, $vessel_arrive, $tenant, $type)
    {
        $vessel_arrive = date('Y-m-d', strtotime($vessel_arrive));
        $port_service = substr($portService, 0, 7);
        $ps_tenant = DB::table('MPLPS')
            ->where('TenantKey', '=', $this->getTenantByName($tenant))
            ->whereRaw("
                '${vessel_arrive}' >= convert(varchar, PeriodStart, 23)
                AND '${vessel_arrive}' <= convert(varchar, PeriodEnd, 23) AND Type='Tenant'
            ")
            ->count();

        // dd($ps_tenant);

        if ($ps_tenant > 0) {
            $data_ps = DB::table('MPS AS T1')
                ->leftJoin('MPLPS AS T2', 'T1.DocEntry', 'T2.PSKey')
                ->leftJoin('M_Currency AS T3', 'T2.Currency', 'T3.DocEntry')
                ->whereRaw("
                    '${vessel_arrive}' >= convert(varchar, T2.PeriodStart, 23)
                    AND '${vessel_arrive}' <= convert(varchar, T2.PeriodEnd, 23) AND T2.Type='Tenant'
                ")
                ->selectRaw("
                    CONCAT(T1.ItemCode, ' - ', T1.FrgnName) AS Name
                    ,T2.Price
                    ,T3.Currency
                ")
                ->where('T2.TenantKey', '=', $this->getTenantByName($tenant))
                ->where('T1.Active', '=', 'Y')
                ->where('T1.ItemCode', '=', $port_service)
                ->first();

            // dd($data_ps);
        } else {
            $data_ps = DB::table('MPS AS T1')
                ->leftJoin('MPLPS AS T2', 'T1.DocEntry', 'T2.PSKey')
                ->leftJoin('M_Currency AS T3', 'T2.Currency', 'T3.DocEntry')
                ->whereRaw("
                '${vessel_arrive}' >= convert(varchar, T2.PeriodStart, 23)
                AND '${vessel_arrive}' <= convert(varchar, T2.PeriodEnd, 23) AND T2.Type='Global'
            ")
                ->selectRaw("
                CONCAT(T1.ItemCode, ' - ', T1.FrgnName) AS Name
                ,T2.Price
                ,T3.Currency
            ")
                ->where('T1.Active', '=', 'Y')
                ->where('T1.ItemCode', '=', $port_service)
                ->first();
        }

        return $data_ps;
    }

    /**
     * @param $weight
     * @param $vessel_arrive
     * @param $tenant
     *
     * @return \Illuminate\Database\Eloquent\Model|\Illuminate\Database\Query\Builder|object|null
     */
    public function calcTotalServiceLoading($vessel_arrive, $tenant)
    {
        $vessel_arrive = date('Y-m-d', strtotime($vessel_arrive));
        $sl_tenant = DB::table('MPLSL')
            ->where('TenantKey', '=', $this->getTenantByName($tenant))
            ->whereRaw("
                '${vessel_arrive}' >= convert(varchar, PeriodStart, 23)
                AND '${vessel_arrive}' <= convert(varchar, PeriodEnd, 23) AND Type='Tenant'
            ")
            ->count();
        if ($sl_tenant > 0) {
            $data_sl = DB::table('MPLSL AS T2')
                ->leftJoin('M_Currency AS T3', 'T2.Currency', 'T3.DocEntry')
                ->whereRaw("
                    '${vessel_arrive}' >= convert(varchar, T2.PeriodStart, 23)
                    AND '${vessel_arrive}' <= convert(varchar, T2.PeriodEnd, 23) AND T2.Type='Tenant'
                ")
                ->selectRaw('
                    T2.Price
                    ,T3.Currency
                ')
                ->where('T2.TenantKey', '=', $this->getTenantByName($tenant))
                ->first();
        } else {
            $data_sl = DB::table('MPLSL AS T2')
                ->leftJoin('M_Currency AS T3', 'T2.Currency', 'T3.DocEntry')
                ->whereRaw("
                '${vessel_arrive}' >= convert(varchar, T2.PeriodStart, 23)
                AND '${vessel_arrive}' <= convert(varchar, T2.PeriodEnd, 23) AND T2.Type='Global'
            ")
                ->selectRaw('
                T2.Price
                ,T3.Currency
            ')
                ->first();
        }
        return $data_sl;
    }

    /**
     * @param $portService
     * @param $vessel_arrive
     * @param $tenant
     * @param $type
     *
     * @return mixed
     */
    public function getPrice($portService, $vessel_arrive, $tenant, $type)
    {
        $vessel_arrive = date('Y-m-d', strtotime($vessel_arrive));
        $port_service = substr($portService, 0, 7);
        $ps_tenant = DB::table('MPLPS')
            ->where('TenantKey', '=', $this->getTenantByName($tenant))
            ->whereRaw("
                '${vessel_arrive}' >= convert(varchar, PeriodStart, 23)
                 AND '${vessel_arrive}' <= convert(varchar, PeriodEnd, 23) AND Type='Tenant'
            ")
            ->count();
        if ($ps_tenant > 0) {
            $data_ps = DB::table('MPS AS T1')
                ->leftJoin('MPLPS AS T2', 'T1.DocEntry', 'T2.PSKey')
                ->leftJoin('M_Currency AS T3', 'T2.Currency', 'T3.DocEntry')
                ->whereRaw("
                    '${vessel_arrive}' >= convert(varchar, T2.PeriodStart, 23)
                    AND '${vessel_arrive}' <= convert(varchar, T2.PeriodEnd, 23) AND T2.Type='Tenant'
                ")
                ->selectRaw("
                    CONCAT(T1.ItemCode, ' - ', T1.FrgnName) AS Name
                    ,T2.Price
                    ,T3.Currency
                ")
                ->where('T2.TenantKey', '=', $this->getTenantByName($tenant))
                ->where('T1.Active', '=', 'Y')
                ->where('T1.ItemCode', '=', $port_service)
                ->first();
        } else {
            $data_ps = DB::table('MPS AS T1')
                ->leftJoin('MPLPS AS T2', 'T1.DocEntry', 'T2.PSKey')
                ->leftJoin('M_Currency AS T3', 'T2.Currency', 'T3.DocEntry')
                ->whereRaw("
                '${vessel_arrive}' >= convert(varchar, T2.PeriodStart, 23)
                AND '${vessel_arrive}' <= convert(varchar, T2.PeriodEnd, 23) AND T2.Type='Global'
            ")
                ->selectRaw("
                CONCAT(T1.ItemCode, ' - ', T1.FrgnName) AS Name
                ,T2.Price
                ,T3.Currency
            ")
                ->where('T1.Active', '=', 'Y')
                ->where('T1.ItemCode', '=', $port_service)
                ->where('T2.Type', '=', 'Global')
                ->first();
        }
        if ($data_ps) {
            return $data_ps->Price;
        }
        return response()->json([
            'errors' => true,
            'message' => 'Please define port service price first!',
        ]);
    }

    /**
     * @param $vessel_arrive
     * @param $tenant
     *
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function getPriceServiceLoading($vessel_arrive, $tenant)
    {
        $vessel_arrive = date('Y-m-d', strtotime($vessel_arrive));
        $ps_tenant = DB::table('MPLSL')
            ->where('TenantKey', '=', $this->getTenantByName($tenant))
            ->whereRaw("
                '${vessel_arrive}' >= convert(varchar, PeriodStart, 23)
                AND '${vessel_arrive}' <= convert(varchar, PeriodEnd, 23) AND Type='Tenant'
            ")
            ->count();
        if ($ps_tenant > 0) {
            $data_ps = DB::table('MPLSL AS T2')
                ->leftJoin('M_Currency AS T3', 'T2.Currency', 'T3.DocEntry')
                ->whereRaw("
                    '${vessel_arrive}' >= convert(varchar, T2.PeriodStart, 23)
                    AND '${vessel_arrive}' <= convert(varchar, T2.PeriodEnd, 23) AND T2.Type='Tenant'
                ")
                ->where('TenantKey', '=', $this->getTenantByName($tenant))
                ->selectRaw('
                    T2.Price
                    ,T3.Currency
                ')
                ->first();
        } else {
            $data_ps = DB::table('MPLSL AS T2')
                ->leftJoin('M_Currency AS T3', 'T2.Currency', 'T3.DocEntry')
                ->whereRaw("
                    '${vessel_arrive}' >= convert(varchar, T2.PeriodStart, 23)
                    AND '${vessel_arrive}' <= convert(varchar, T2.PeriodEnd, 23) AND T2.Type='Global'
                ")
                ->selectRaw('
                    T2.Price
                    ,T3.Currency
                ')
                ->first();
        }

        return $data_ps->Price;
    }

    /**
     * @param $key
     * @param $array
     *
     * @return mixed|null
     */
    public function arrayExist($key, $array)
    {
        if (array_key_exists($key, $array)) {
            return $array[$key];
        }
        return null;
    }

    /**
     * @param $request
     * @param $type
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function billingDetails($request, $type)
    {
        $query = DB::table('BEXP1 AS T0')
            ->leftJoin('M_Tenant AS T2', 'T2.DocEntry', 'T0.TenantId')
            ->leftJoin('users AS T3', 'T3.id', 'T0.CreatedBy')
            ->LeftJoin('M_Jetty AS T4', 'T0.Jetty', 'T4.DocEntry')
            ->leftJoin('M_BP AS T5', 'T0.BP', 'T5.DocEntry')
            ->selectRaw('
                    T2.Name AS Tenant
                    ,T0.*
                    ,T4.Name As Jetty
                    ,T5.Name AS BP
                ')
            ->where('T0.ExportId', '=', $request->ExportId)
            ->where('T0.Type', '=', $type)
            ->get();

        return response()->json([
            'rows' => $query,
        ]);
    }

    /**
     * @param $request
     * @param $type
     * @param $price
     * @param $header
     * @param $detail
     * @param $port_service
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function storeBillingDetail($request, $type, $price, $header, $detail, $port_service)
    {
        if ($type == 'IN' || $type == 'OUT') {
            $ps_type = 'Local';
        } else {
            $ps_type = $type;
        }

        $ps_total = $this->calcTotal($port_service, $request->form['VesselArrival'], $detail['Tenant'], $ps_type);
        $sl_total = $this->calcTotalServiceLoading($request->form['VesselArrival'], $detail['Tenant']);

        if ($header) {
            $header->TenantId = $this->getTenantByName($detail['Tenant']);
            $header->PortServiceType = $detail['PortServiceType'];
            $header->WeightCategory = $detail['WeightCategory'];
            $header->LoadingUnloadingType = $detail['LoadingUnloadingType'];
            $header->CompanyHeader = $detail['CompanyHeader'];
            $header->Signature1 = $detail['Signature1'];
            $header->Signature2 = $detail['Signature2'];
            $header->Signature3 = $detail['Signature3'];
            $header->NoBL = key_exists('NoBL', $detail) ? $detail['NoBL'] : '';
            $header->DateBL = key_exists('DateBL', $detail) ? $this->toDate($detail['DateBL']) : null;
            // $header->DateBL = ($detail["DateBL"]) ? $this->toDate($detail["DateBL"]) : null;
            $header->LoadingItem = $detail['LoadingItem'];
            $header->Classification = key_exists('Classification', $detail) ? $detail['Classification'] : '';
            $header->LoadingQty = $detail['LoadingQty'];
            $header->AttachmentText = $detail['AttachmentText'];
            $header->ExportId = $request->exportId;
            $header->BP = key_exists('BP', $detail) ? $this->getDocEntryBP($detail['BP']) : 0;
            $header->Jetty = $detail['Jetty'] ? $this->getJettyByName($detail['Jetty']) :
                $this->getJettyByName($request->dataExport['Jetty']);
            $header->Price = $this->getPrice(
                $port_service,
                $request->form['VesselArrival'],
                $detail['Tenant'],
                $ps_type
            );
            //            $header->Price = $detail['Price'];
            $header->ServiceLoading = $this->getPriceServiceLoading(
                $request->form['VesselArrival'],
                $detail['Tenant']
            );
            $header->Total = $detail['Tenant'] != 'BDM' ? round(($ps_total->Price * $detail['LoadingQty']), 2) : 0;
            $header->CurrencyPortService = $ps_total->Currency;
            $header->TotalServiceLoading = $detail['Tenant'] != 'BDM' ? round(($sl_total->Price * $detail['LoadingQty']), 2) : 0;
            $header->CurrencyServiceLoading = $sl_total->Currency;
            //$header->Total = $this->calcTotal($detail['PortServiceType'], $detail['LoadingQty']);
            $header->updated_at = Carbon::now();
            $header->UpdateBy = $request->user()->id;
            $header->save();
            $doc_num = $header->DocEntry;
        } else {
            $header = new BEXP1();
            $header->TenantId = $this->getTenantByName($detail['Tenant']);
            $header->PortServiceType = $detail['PortServiceType'];
            $header->WeightCategory = $detail['WeightCategory'];
            $header->LoadingUnloadingType = $detail['LoadingUnloadingType'];
            $header->CompanyHeader = $detail['CompanyHeader'];
            $header->Signature1 = $detail['Signature1'];
            $header->Signature2 = $detail['Signature2'];
            $header->Signature3 = $detail['Signature3'];
            $header->NoBL = key_exists('NoBL', $detail) ? $detail['NoBL'] : '';
            $header->DateBL = key_exists('DateBL', $detail) ? $this->toDate($detail['DateBL']) : null;
            $header->BP = key_exists('BP', $detail) ? $this->getDocEntryBP($detail['BP']) : 0;
            $header->LoadingItem = $detail['LoadingItem'];
            $header->Classification = key_exists('Classification', $detail) ? $detail['Classification'] : '';
            $header->LoadingQty = $detail['LoadingQty'];
            $header->AttachmentText = $detail['AttachmentText'];
            $header->Jetty = $detail['Jetty'] ? $this->getJettyByName($detail['Jetty']) :
                $this->getJettyByName($request->dataExport['Jetty']);
            $header->ExportId = $request->exportId;
            $header->Price = array_key_exists('Price', $detail) ? $price : null;
            $header->Total = $detail['Tenant'] != 'BDM' ? round(($ps_total->Price * $detail['LoadingQty']), 2) : 0;
            $header->CurrencyPortService = $ps_total->Currency;
            $header->TotalServiceLoading = $detail['Tenant'] != 'BDM' ? round(($sl_total->Price * $detail['LoadingQty']), 2) : 0;
            $header->CurrencyServiceLoading = $sl_total->Currency;
            $header->ServiceLoading = $this->getPriceServiceLoading(
                $request->form['VesselArrival'],
                $detail['Tenant']
            );
            $header->created_at = Carbon::now();
            $header->CreatedBy = $request->user()->id;
            $header->UpdateBy = 0;
            $header->Type = $type;
            $header->save();
            $doc_num = $header->DocEntry;
        }

        return $doc_num;
    }

    /**
     * @param $request
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function dataPortService($request)
    {
        $vessel_arrive = $request->vesselArrive;
        if (empty($vessel_arrive)) {
            return response()->json([
                'message' => 'Plese fill vessel departure first!'
            ], 422);
        }

        $vessel_arrive = date('Y-m-d', strtotime($vessel_arrive));

        $billing_type = $request->billingType;
        if ($request->pluck == 1) {
            $port_pluck = DB::table('MPS AS T1')
                ->leftJoin('MPLPS AS T2', 'T1.DocEntry', 'T2.PSKey')
                ->leftJoin('M_Currency AS T3', 'T2.Currency', 'T3.DocEntry')
                ->whereRaw("
                    '${vessel_arrive}' >= convert(varchar, T2.PeriodStart, 23)
                    AND '${vessel_arrive}' <= convert(varchar, T2.PeriodEnd, 23) AND T2.Type='Global'
                ")
                ->selectRaw("
                    CONCAT(T1.ItemCode, ' - ', T1.FrgnName) AS ItemCode
                ")
                ->where('T1.Active', '=', 'Y')
                ->pluck('ItemCode')
                ->toArray();
        }

        $port_service = DB::table('MPS AS T1')
            ->leftJoin('MPLPS AS T2', 'T1.DocEntry', 'T2.PSKey')
            ->leftJoin('M_Currency AS T3', 'T2.Currency', 'T3.DocEntry')
            ->whereRaw("
                '${vessel_arrive}' >= convert(varchar, T2.PeriodStart, 23)
                AND '${vessel_arrive}' <= convert(varchar, T2.PeriodEnd, 23) AND T2.Type='Global'
            ")
            ->selectRaw("
                CONCAT(T1.ItemCode, ' - ', T1.FrgnName) AS Name
                ,T1.ItemCode
                ,T2.Price
                ,T3.Currency
                ,T2.BillingType
                ,T2.Type
            ")
            ->where('T1.Active', '=', 'Y')
            ->get();

        $ps_master = DB::table('MPS')
            ->selectRaw('ItemCode AS Name, ItemName')
            ->where('Active', '=', 'Y')
            ->get();

        $service_loading = DB::table('MPLSL')
            ->whereRaw("
                '${vessel_arrive}' >= convert(varchar, PeriodStart, 23)
                AND '${vessel_arrive}' <= convert(varchar, PeriodEnd, 23) AND Type='Global'
            ")
            ->first();

        $sl_tenant = DB::table('MPLSL AS T0')
            ->leftJoin('M_Tenant as T1', 'T1.DocEntry', 'T0.TenantKey')
            ->whereRaw("
                '${vessel_arrive}' >= convert(varchar, PeriodStart, 23)
                AND '${vessel_arrive}' <= convert(varchar, PeriodEnd, 23) AND T0.Type='Tenant'
            ")
            ->distinct()
            ->pluck('T1.Name')
            ->toArray();

        $ps_tenant = DB::table('MPLPS AS T2')
            ->leftJoin('M_Tenant AS T3', 'T2.TenantKey', 'T3.DocEntry')
            ->leftJoin('MPS AS T4', 'T4.DocEntry', 'T2.PSKey')
            ->leftJoin('M_Currency AS T5', 'T2.Currency', 'T5.DocEntry')
            ->whereRaw("
                '${vessel_arrive}' >= convert(varchar, T2.PeriodStart, 23)
                AND '${vessel_arrive}' <= convert(varchar, T2.PeriodEnd, 23) AND T2.Type='Tenant'
            ")
            ->selectRaw('
                T3.Name
            ')
            ->where('T4.Active', '=', 'Y')
            ->distinct()
            ->pluck('Name')
            ->toArray();

        $port_service_tenant = DB::table('MPS AS T1')
            ->leftJoin('MPLPS AS T2', 'T1.DocEntry', 'T2.PSKey')
            ->leftJoin('M_Currency AS T3', 'T2.Currency', 'T3.DocEntry')
            ->leftJoin('M_Tenant as T4', 'T4.DocEntry', 'T2.TenantKey')
            ->whereRaw("
                '${vessel_arrive}' >= convert(varchar, T2.PeriodStart, 23)
                AND '${vessel_arrive}' <= convert(varchar, T2.PeriodEnd, 23) AND T2.Type='Tenant'
            ")
            ->selectRaw("
                CONCAT(T1.ItemCode, ' - ', T1.FrgnName) AS Name
                ,T1.ItemCode
                ,T2.Price
                ,T3.Currency
                ,T2.BillingType
                ,T2.Type
                ,T4.Name AS Tenant
            ")
            ->where('T1.Active', '=', 'Y')
            ->get();

        $weight_category = [
            'B/L Weight',
            'Net Weight',
        ];

        return response()->json([
            'rows' => $port_service,
            'master' => $ps_master,
            'name' => $port_pluck,
            'weight_category' => $weight_category,
            'ps_tenant' => $ps_tenant,
            'ps_tenant_value' => $port_service_tenant,
            'service_loading' => $service_loading,
            'sl_tenant' => $sl_tenant,
        ]);
    }

    /**
     * @param $request
     * @param $billing_type
     *
     *
     * @throws \Exception
     */
    public function dataReportBilling($request, $billing_type)
    {
        $doc_entry = isset($request->docEntry) ? (int) $request->docEntry : null;
        $year_export = isset($request->yearExport) ? (string) $request->yearExport : null;
        $vessel_name = isset($request->vessel) ? (string) $request->vessel : null;
        if ($vessel_name) {
            $master_vessel = DB::table('M_CARGO')->where('name', '=', $vessel_name)->first();
            $vessel_type = isset($request->vesselType) ? (string) $request->vesselType : $master_vessel->Type;
        }
        $export_type = isset($request->exportType) ? (string) $request->exportType : null;
        $report_type = isset($request->type) ? (string) $request->type : null;
        $voyage = isset($request->voyage) ? (string) $request->voyage : null;

        // dd($voyage);
        Carbon::setLocale('id_ID');
        //setlocale (LC_TIME, 'id_ID');
        // $footer_date = Carbon::parse($year_export)->isoFormat('DD MMMM YYYY');
        $footer_date = Carbon::parse($year_export);
        // info("year export " . $year_export, [
        //     'date' => $footer_date,
        // ]);
        if ($export_type == 'port_service_service_loading') {
            $ps_imip = DB::select(
                "EXEC PortService_Report '${doc_entry}', '${billing_type}'"
            );
            $ps_imip_sum = DB::select(
                "EXEC PortService_Report_Sum '${doc_entry}', 'IMIP', '${billing_type}'"
            );
            $ps_bdt_sums = DB::select(
                "EXEC PortService_Report_Sum '${doc_entry}', 'BDT', '${billing_type}'"
            );
            $service_loading = DB::select(
                "EXEC ServiceLoading_Report '${doc_entry}', '${billing_type}', 'Yes', 'Yes'"
            );
            $sl_sums = DB::select(
                "EXEC ServiceLoading_Report_Sum '${doc_entry}', '${billing_type}', 'Yes', 'Yes'"
            );

            $start_time = microtime(true);

            $user_id = $request->user()->id;
            //$path_file = $doc_entry;
            $data_voyage = preg_replace("/:?(\s+)?(<?>?\.?\/?)/", '', $voyage);
            $data_vessel_name = preg_replace("/:?(\s+)?(<?>?\.?\/?)/", '', $vessel_name);

            $path_file = preg_replace("/:?(\s+)?(<?>?\.?\/?)/", '', $data_vessel_name) . '_V_' . $data_voyage;
            $destination_path = public_path("pdf/billing/${billing_type}/${user_id}/${path_file}/");
            $files = glob("${destination_path}/*"); // get all file names
            foreach ($files as $data_file) { // iterate files
                if (is_file($data_file)) {
                    unlink($data_file);
                } // delete file
            }

            if (!file_exists($destination_path)) {
                if (!mkdir($destination_path, 0777, true) && !is_dir($destination_path)) {
                    throw new \RuntimeException(
                        sprintf(
                            'Directory "%s" was not created',
                            $destination_path
                        )
                    );
                }
            }

            // Load DOM PDF
            if (count($ps_imip) > 0) {
                if ($vessel_type != 'SPB1' && $vessel_type != 'SBOB') {
                    $pdf_file_imip_summary = PDF::loadView('export.billing_export.portServiceSummary', [
                        'dataExport' => $ps_imip,
                        'summary_imip' => $ps_imip_sum,
                        'summary_bdt' => $ps_bdt_sums,
                        'vesselName' => $vessel_name,
                        'vessel_type' => $vessel_type,
                        'voyage' => $voyage,
                        'footer_date' => $footer_date,
                        'billing_type' => $billing_type,
                        'companyHeader' => 'imip',
                    ]);

                    $file_name = 'PS_' . preg_replace("/:?(\s+)?(<?>?\.?\/?)/", '', $data_vessel_name) . '_V_' . $data_voyage . '_SUMMARY.pdf';

                    file_put_contents($destination_path . $file_name, $pdf_file_imip_summary->output());
                    $all_pdf[] = $destination_path . $file_name;
                    // Remove Attachment
                    RemoveAttachment::dispatch($destination_path . $file_name)->delay(now()->addMinutes(5));
                }

                $pdf_file_imip = PDF::loadView('export.billing_export.portService', [
                    'dataExport' => $ps_imip,
                    'summary_imip' => $ps_imip_sum,
                    'summary_bdt' => $ps_bdt_sums,
                    'vesselName' => $vessel_name,
                    'vessel_type' => $vessel_type,
                    'voyage' => $voyage,
                    'footer_date' => $footer_date,
                    'billing_type' => $billing_type,
                    'companyHeader' => 'imip',
                ]);

                $file_name = 'PS_' . preg_replace("/:?(\s+)?(<?>?\.?\/?)/", '', $data_vessel_name) . '_V_' . $data_voyage . '_DETAILS.pdf';

                file_put_contents($destination_path . $file_name, $pdf_file_imip->output());
                $all_pdf[] = $destination_path . $file_name;
                // Remove Attachment
                RemoveAttachment::dispatch($destination_path . $file_name)->delay(now()->addMinutes(5));
            }

            // Load DOM PDF
            if (count($service_loading) > 0) {
                if ($vessel_type != 'SPB1' && $vessel_type != 'SBOB') {
                    $pdf_file_sl_summary = PDF::loadView('export.billing_export.serviceLoadingSummary', [
                        'dataExport' => $service_loading,
                        'summary' => $sl_sums,
                        'vesselName' => $vessel_name,
                        'vessel_type' => $vessel_type,
                        'voyage' => $voyage,
                        'billing_type' => $billing_type,
                        'footer_date' => $footer_date,
                    ]);

                    $file_name = 'SL_' . preg_replace("/:?(\s+)?(<?>?\.?\/?)/", '', $data_vessel_name) . '_V_' . $data_voyage . '_SUMMARY.pdf';
                    file_put_contents($destination_path . $file_name, $pdf_file_sl_summary->output());
                    $all_pdf[] = $destination_path . $file_name;
                    // Remove Attachment
                    RemoveAttachment::dispatch($destination_path . $file_name)->delay(now()->addMinutes(5));
                }

                $pdf_file_sl = PDF::loadView('export.billing_export.serviceLoading', [
                    'dataExport' => $service_loading,
                    'summary' => $sl_sums,
                    'vesselName' => $vessel_name,
                    'vessel_type' => $vessel_type,
                    'voyage' => $voyage,
                    'billing_type' => $billing_type,
                    'footer_date' => $footer_date,
                ]);

                $file_name = 'SL_' . preg_replace("/:?(\s+)?(<?>?\.?\/?)/", '', $data_vessel_name) . '_V_' . $data_voyage . '_DETAILS.pdf';
                file_put_contents($destination_path . $file_name, $pdf_file_sl->output());
                $all_pdf[] = $destination_path . $file_name;
                // Remove Attachment
                RemoveAttachment::dispatch($destination_path . $file_name)->delay(now()->addMinutes(5));
            }
            // store excel imip

            Storage::disk('local')->makeDirectory('billing');

            if (count($ps_imip) > 0) {
                $file_name = 'PS_' . preg_replace("/:?(\s+)?(<?>?\.?\/?)/", '', $data_vessel_name) . '_V_' . $data_voyage . '.xlsx';

                Excel::store(
                    new ServiceLoadingPortServiceExport(
                        $ps_imip,
                        str_replace(' & ', ' &amp; ', $vessel_name),
                        $voyage,
                        $billing_type,
                        'ps'
                    ),
                    '/billing/' . $file_name
                );
                $all_pdf[] = storage_path('app/billing/') . $file_name;
                // Remove Attachment
                RemoveAttachment::dispatch(storage_path('app/billing/') . $file_name)->delay(now()->addMinutes(5));
            }

            if (count($service_loading) > 0) {
                // store excel service loading
                $file_name = 'SL_' . preg_replace("/:?(\s+)?(<?>?\.?\/?)/", '', $data_vessel_name) . '_V_' . $data_voyage . '.xlsx';

                $end_time = microtime(true);
                Excel::store(
                    new ServiceLoadingPortServiceExport(
                        $service_loading,
                        str_replace(' & ', ' &amp; ', $vessel_name),
                        $voyage,
                        $billing_type,
                        'sl'
                    ),
                    '/billing/' . $file_name
                );
                $all_pdf[] = storage_path('app/billing/') . $file_name;
                RemoveAttachment::dispatch(storage_path('app/billing/') . $file_name)->delay(now()->addMinutes(5));
            }

            $zipper = new Zipper();
            $zip_path = public_path() . '/zip/' . $request->user()->id . '/BILLING_EXPORT/';
            if (!file_exists($zip_path)) {
                if (!mkdir($zip_path, 0777, true) && !is_dir($zip_path)) {
                    throw new \RuntimeException(
                        sprintf(
                            'Directory "%s" was not created',
                            $zip_path
                        )
                    );
                }
            }
            $data_file = public_path(
                '/zip/' . $request->user()->id . "/BILLING/${billing_type}/" . $path_file . '.zip'
            );
            if (file_exists($data_file)) {
                unlink($data_file);
            }
            $zipper->make($data_file)->add($all_pdf);
            $zipper->close();
            $headers = [
                'Content-Type: application/zip',
            ];

            RemoveAttachment::dispatch($data_file)->delay(now()->addMinutes(5));
            $response = '/zip/' . $request->user()->id . "/BILLING/${billing_type}/" . $path_file . '.zip';
        } elseif ($export_type == 'time_sheet_berita_acara') {
            // Time Sheet dan berta acara
            Carbon::setLocale('id_ID');
            $vessel_data = DB::table('BHLOCAL AS T1')
                ->leftJoin('L_Master AS T2', 'T1.LocalID', 'T2.DocEntry')
                ->leftJoin('BEXP AS T3', 'T1.DocEntry', 'T3.DocNum')
                ->leftJoin('M_Jetty AS T5', 'T5.DocEntry', 'T1.Jetty')
                ->leftJoin('M_Tenant AS T6', 'T6.DocEntry', 'T3.TenantId')
                ->leftJoin('M_BP AS T7', 'T7.DocEntry', 'T3.BP')
                ->where('T1.DocEntry', '=', $doc_entry)
                ->where('T1.Type', '=', $report_type)
                ->where('T3.Type', '=', $report_type)
                ->selectRaw("IIF(T2.VesselType = 'MV', (SELECT Name FROM M_CARGO WHERE DocEntry = T2.VesselName),
                               (SELECT Name FROM M_CARGO WHERE DocEntry = T2.VesselName)) AS VesselName
                                ,(select name from M_CARGO Z where Z.DocEntry= T2.tongkang ) as Tongkang
                                , 'INDONESIA' AS Flag
                                , T3.LoadingItem
                                , T3.LoadingQty
                                , T3.WeightCategory
                                , T5.Name AS Jetty
                                , T6. Name As Tenant
                                , T2.VesselDeparture
                                , T7. Name As BusinessPartner
                                , T2.TransType
                                , T2.VesselArrival
                                , T2.VesselDeparture
                                , T2.TglSandar AS BerthingDate
                                , T2.TglLabuh AS AnchorageDate
                                , T2.UnloadingDate
                                , T2.FinishUnloadingDate
                          ")
                ->first();
            $all_pdf = [];

            $time_sheets = DB::table('TimeSheet')
                ->where('BillingHeader', '=', $doc_entry)
                ->orderBy('LineNum', 'ASC')
                ->get();

            if (!file_exists(public_path('zip/' . Auth::user()->id))) {
                mkdir(public_path('zip/' . Auth::user()->id), 0700, true);
            }

            $file_path_name = public_path(
                'zip/' . Auth::user()->id . '/berita_acara_' . strtoupper(Str::slug($vessel_data->VesselName)) . '_' . '.docx'
            );
            $all_pdf[] = $file_path_name;
            $letter_template = new TemplateProcessor(public_path('template/berita_acara.docx'));
            $letter_template->setValue('NO_SURAT', date('d / m / Y', strtotime($vessel_data->VesselDeparture)));
            // $letter_template->setValue("NO_SURAT", date('d / m / Y'));
            $letter_template->setValue('VESSEL_NAME', $vessel_data->VesselName . ' /' . $vessel_data->Tongkang);
            $letter_template->setValue('ITEM_NAME', strtoupper($vessel_data->LoadingItem));
            $letter_template->setValue('GROSS_WEIGHT', number_format($vessel_data->LoadingQty, 3) . ' MT');
            $letter_template->setValue('BUSINESS_PARTNER', $vessel_data->BusinessPartner);
            $letter_template->setValue('ATTCH', $vessel_data->WeightCategory);
            $letter_template->setValue('TENANT', 'PT. ' . $vessel_data->Tenant);
            $letter_template->setValue('T_AR', !empty($vessel_data->VesselArrival) ?
                Carbon::parse($vessel_data->VesselArrival)->format('H:i') : null);
            $letter_template->setValue('DATE_ARRIVAL', !empty($vessel_data->VesselArrival) ?
                strtoupper(Carbon::parse($vessel_data->VesselArrival)->format('Y F d')) : null);
            $letter_template->setValue('T_AN', !empty($vessel_data->AnchorageDate) ?
                Carbon::parse($vessel_data->AnchorageDate)->format('H:i') : null);
            $letter_template->setValue('DATE_ANCHORAGE', !empty($vessel_data->AnchorageDate) ?
                strtoupper(Carbon::parse($vessel_data->AnchorageDate)->format('Y F d')) : null);
            $letter_template->setValue('T_BE', !empty($vessel_data->BerthingDate) ?
                Carbon::parse($vessel_data->BerthingDate)->format('H:i') : null);
            $letter_template->setValue('DATE_BERTHING', !empty($vessel_data->BerthingDate) ?
                strtoupper(Carbon::parse($vessel_data->BerthingDate)->format('Y F d')) : null);
            $letter_template->setValue('T_UN', !empty($vessel_data->UnloadingDate) ?
                Carbon::parse($vessel_data->UnloadingDate)->format('H:i') : null);
            $letter_template->setValue('DATE_UNLOADING', !empty($vessel_data->UnloadingDate) ?
                strtoupper(Carbon::parse($vessel_data->UnloadingDate)->format('Y F d')) : null);
            $letter_template->setValue('T_FI', !empty($vessel_data->FinishUnloadingDate) ?
                Carbon::parse($vessel_data->FinishUnloadingDate)->format('H:i') : null);
            $letter_template->setValue('DATE_FINISH_UNLOADING', !empty($vessel_data->FinishUnloadingDate)
                ? strtoupper(Carbon::parse($vessel_data->FinishUnloadingDate)->format('Y F d')) : null);
            $letter_template->setValue('T_DE', !empty($vessel_data->VesselDeparture) ?
                Carbon::parse($vessel_data->VesselDeparture)->format('H:i') : null);
            $letter_template->setValue('DATE_DEPARTURE', !empty($vessel_data->VesselDeparture) ?
                strtoupper(Carbon::parse($vessel_data->VesselDeparture)->format('Y F d')) : null);
            $letter_template->setValue(
                'JETTY',
                strtoupper(
                    $vessel_data->Jetty
                )
            );
            $letter_template->saveAs($file_path_name);

            RemoveAttachment::dispatch($file_path_name)->delay(now()->addMinutes(5));

            if (count($time_sheets) > 0) {
                Carbon::setLocale('en');
                $letter_template = new TemplateProcessor(public_path('template/time_sheet.docx'));
                $letter_template->setValue('VESSEL_NAME', $vessel_data->VesselName . ' /' . $vessel_data->Tongkang);
                $letter_template->setValue('FLAG', $vessel_data->Flag);
                $letter_template->setValue('ITEM_NAME', strtoupper($vessel_data->LoadingItem));
                $letter_template->setValue(
                    'JETTY',
                    strtoupper(
                        $vessel_data->Jetty . ' (PT. IMIP), bahodopi anchorage, central sulawesi province, indonesia'
                    )
                );

                $data_letter = [];
                $file_path_name = public_path(
                    'zip/' . Auth::user()->id . '/time_sheet_' . strtoupper(Str::slug($vessel_data->VesselName)) . '_' . '.docx'
                );
                $all_pdf[] = $file_path_name;

                $previous_date = Carbon::parse($time_sheets[0]->DocDate)->format('d-M-Y');
                $document_date = '';
                // dd($previous_date);
                foreach ($time_sheets as $time_sheet) {
                    if ($time_sheet->DocDate) {
                        $day_date = Carbon::parse($time_sheet->DocDate)->shortDayName;
                        $previous_date = $time_sheet->DocDate;
                    } else {
                        $day_date = Carbon::parse($previous_date)->shortDayName;
                    }
                    $doc_date = Carbon::parse($time_sheet->DocDate)->format('d-M-Y');

                    $data_letter[] = [
                        'DATE' => $time_sheet->DocDate ? $doc_date : '',
                        'DAY' => $time_sheet->DocDate ? Carbon::parse($time_sheet->DocDate)->shortDayName : '',
                        // "DAY" => Carbon::parse($time_sheet->DocDate)->shortDayName,
                        'FROM' => $time_sheet->TimeFrom ? date('H:i', strtotime($time_sheet->TimeFrom)) : '',
                        'TO' => $time_sheet->TimeTo ? date('H:i', strtotime($time_sheet->TimeTo)) : '',
                        'WEATHER' => str_replace('&', '&amp;', $time_sheet->Weather),
                        'GANGS' => $time_sheet->NoGangs,
                        'REMARK' => $time_sheet->Remarks,
                    ];
                }
                // dd($data_letter);
                $letter_template->cloneRowAndSetValues('DATE', $data_letter);
                $letter_template->saveAs($file_path_name);

                RemoveAttachment::dispatch($file_path_name)->delay(now()->addMinutes(5));
                $response = '/zip/' . Auth::user()->id . '/time_sheet_' .
                    strtoupper(Str::slug($vessel_data->VesselName)) . '_' . '.docx';
            } else {
                return response()->json([
                    'message' => 'Please generate timesheet first'
                ], 422);
            }

            $zipper = new Zipper();
            $zip_path = public_path() . '/zip/' . $request->user()->id . "/BILLING/{$vessel_data->TransType}/";
            if (!file_exists($zip_path)) {
                if (!mkdir($zip_path, 0777, true) && !is_dir($zip_path)) {
                    throw new \RuntimeException(
                        sprintf(
                            'Directory "%s" was not created',
                            $zip_path
                        )
                    );
                }
            }
            $data_file = public_path(
                '/zip/' . $request->user()->id . "/BILLING/{$vessel_data->TransType}/berita_acara_time_sheet.zip"
            );
            if (file_exists($data_file)) {
                unlink($data_file);
            }
            $zipper->make($data_file)->add($all_pdf);

            RemoveAttachment::dispatch($data_file)->delay(now()->addMinutes(5));
            $zipper->close();
            $response = '/zip/' . $request->user()->id .
                "/BILLING/{$vessel_data->TransType}/berita_acara_time_sheet.zip";
        } elseif ($export_type == 'nota_penagihan') {
            $response = $this->notaPenagihan($doc_entry, $request);
        } elseif ($export_type == 'nota_jkd') {
            $response = $this->dockCleaning($doc_entry, $request, $billing_type);
        } else {
            $data_vessel = DB::select(
                "EXEC ExportLetter '${doc_entry}', '${billing_type}'"
            );

            foreach ($data_vessel as $item) {
                if ($export_type == 'departure_agreement') {
                    $template_surat = new TemplateProcessor(public_path('template/persetujuan_keberangkatan.docx'));
                } else {
                    $template_surat = new TemplateProcessor(public_path('template/persetujuan_sandar.docx'));
                }
                $vessel_berthing = $item->BerthingDate ? $this->returnLocalDate($item->BerthingDate, 'SANDAR') : '';
                $vessel_anchorage = $item->AnchorageDate ? $this->returnLocalDate($item->AnchorageDate, 'LABUH') : '';
                $vessel_arrival = $item->VesselArrival ? $this->returnLocalDate($item->VesselArrival, 'TIBA') : '';
                $vessel_departure = $item->VesselDeparture ? $this->returnLocalDate($item->VesselDeparture, '') : '';

                $vessel_in_letter = $item->Tongkang ? $item->VesselName . ' / ' . $item->Tongkang
                    : $item->VesselName . ' VOY. ' . $item->Voyage;

                $vessel_qty = $item->Tongkang ?
                    'TB. ' . number_format($item->GrossWeight, 0) . ' / ' . ' BG. ' .
                    number_format($item->BargeWeight, 0) . ' TON' :
                    number_format($item->GrossWeight, 3) . ' MT';

                $template_surat->setValue('VESSEL_NAME', strtoupper($vessel_in_letter));
                $template_surat->setValue('FLAG', $item->VesselFlag);
                $template_surat->setValue('GROSS_WEIGHT', $vessel_qty);
                $template_surat->setValue('LOADING_QTY', number_format($item->LoadingQty, 3));
                $template_surat->setValue('ITEM_NAME', strtoupper($item->LoadingItem));
                $template_surat->setValue('DATE_ARRIVAL', strtoupper($vessel_arrival));
                $template_surat->setValue('DATE_BERTHING', strtoupper($vessel_berthing));
                $template_surat->setValue('DATE_ANCHORAGE', strtoupper($vessel_anchorage));
                $template_surat->setValue('DATE_DEPARTURE', strtoupper($vessel_departure));
                $template_surat->setValue('LETTER_DATE', strtoupper($footer_date));
                $template_surat->setValue('JETTY', strtoupper($item->Jetty) . 'PELABUHAN KHUSUS BAHUDOPI (PT. IMIP)');

                $file_path_name = public_path("zip/docx/billing/${billing_type}/" . Auth::user()->id);

                if (!file_exists($file_path_name)) {
                    if (!mkdir($file_path_name, 0777, true) && !is_dir($file_path_name)) {
                        throw new \RuntimeException(
                            sprintf(
                                'Directory "%s" was not created',
                                $file_path_name
                            )
                        );
                    }
                }
                $template_surat->saveAs($file_path_name . "/${export_type}.docx");

                RemoveAttachment::dispatch($file_path_name . "/${export_type}.docx")->delay(now()->addMinutes(5));

                $response = "/zip/docx/billing/${billing_type}/" . Auth::user()->id . "/${export_type}.docx";
            }
        }
        // return response()->download(public_path($response));
        return public_path($response);
    }

    protected function getLatestBiRate($currency, $date)
    {
        $biCurrencyService = new BiCurrencyService();

        // Adjust for weekends: if Saturday (6) or Sunday (0), move to previous Friday
        $carbonDate = Carbon::parse($date);
        if ($carbonDate->isSaturday()) {
            $carbonDate->subDay(); // Friday
        } elseif ($carbonDate->isSunday()) {
            $carbonDate->subDays(2); // Friday
        }
        $queryDate = $carbonDate->format('Ymd');

        // Try to get rate for the (possibly adjusted) date
        $rates = $biCurrencyService->getRate($currency, $queryDate, $queryDate);

        // If no data, try the previous 7 days and get the latest available
        if (empty($rates)) {
            $startDate = $carbonDate->copy()->subDays(6)->format('Ymd');
            $endDate = $carbonDate->format('Ymd');
            $rates = $biCurrencyService->getRate($currency, $startDate, $endDate);

            // Sort by date descending to get the latest
            usort($rates, function ($a, $b) {
                return strtotime($b['date']) - strtotime($a['date']);
            });
        }

        // Return the 'rate' from the latest available entry, or null if not found
        return !empty($rates) ? $rates[0]['rate'] : null;
    }

    protected function dockCleaning($doc_entry, $request, $billing_type)
    {
        try {
            $doc = DB::table('BEXP AS T0')
                ->leftJoin('M_Tenant AS T1', 'T0.ChargeTo', 'T1.FullName')
                ->leftJoin('M_BP AS T2', 'T0.ChargeTo', 'T2.Name')
                ->when($billing_type, function ($query) use ($billing_type) {
                    if ($billing_type == 'Import') {
                        return $query->leftJoin('BHIMP AS T3', 'T0.DocNum', 'T3.DocEntry')
                            ->leftJoin('T_MDOC_Header AS T4', 'T3.ImportID', 'T4.DocEntry')
                            ->leftJoin('M_CARGO AS T5', 'T4.Cargo', 'T5.Name')
                            ->where('T3.Status', '<>', 'Cancel')
                            ->selectRaw("
                                CASE
                                    WHEN T5.Type = 'MV' THEN CONCAT(T5.Name, ' V. ', T4.Voyage)
                                    WHEN T5.Type = 'MT' THEN CONCAT(T5.Name, ' V. ', T4.Voyage)
                                    ELSE T5.Name
                                END as VesselName
                            ");
                    } else if ($billing_type == 'Export') {
                        return $query->leftJoin('BHEXP AS T3', 'T0.DocNum', 'T3.DocEntry')
                            ->leftJoin('THEXP AS T4', 'T3.ExportID', 'T4.DocEntry')
                            ->leftJoin('M_CARGO AS T5', 'T4.VesselName', 'T5.DocEntry')
                            ->where('T3.Status', '<>', 'Cancel')
                            ->selectRaw("
                                CASE
                                    WHEN T5.Type = 'MV' THEN CONCAT(T5.Name, ' V. ', T4.Voyage)
                                    WHEN T5.Type = 'MT' THEN CONCAT(T5.Name, ' V. ', T4.Voyage)
                                    ELSE T5.Name
                                END as VesselName
                            ");
                    } else {
                        return $query->leftJoin('BHLOCAL AS T3', 'T0.DocNum', 'T3.DocEntry')
                            ->leftJoin('L_Master AS T4', 'T3.LocalID', 'T4.DocEntry')
                            ->leftJoin('M_CARGO AS T5', 'T4.VesselName', 'T5.DocEntry')
                            ->where('T3.Status', '<>', 'Cancel')
                            ->select([
                                DB::raw("IIF(T4.VesselType = 'MV', CONCAT(T5.Name, ' V. ', T4.Voyage),
                                CONCAT(T5.Name, ' ', (select name from M_CARGO Z where Z.DocEntry = T4.tongkang ))) AS VesselName")
                            ]);
                    }
                })
                ->selectRaw("
                    T1.Address AS AddressTenant,
                    T2.Address As AddressBp,
                    T0.DockCleaningPrice,
                    T0.DockCleaningAmount,
                    T0.DockCleaningGrantTotal,
                    T0.DockCleaningVat,
                    T0.DockCleaningNo,
                    T0.ChargeTo,
                    T0.NoNota,
                    T0.NoNotaSl,
                    T0.ServiceLoading,
                    T3.BillingNoteDate,
                    T4.VesselDeparture,
                    T0.PriceEstimate,
                    T0.LoadingUnloadingType,
                    T0.PriceRevised,
                    T0.QtyEstimate,
                    T0.QtyRevised,
                    T0.TaxCode,
                    T0.CompanyHeader,
                    T0.Status,
                    T0.DocEntry,
                    CASE
                        WHEN (SELECT COUNT(*) FROM MPLPS
                                WHERE TenantKey = T0.TenantId AND Type ='Tenant'
                                AND T4.VesselDeparture >= PeriodStart AND T4.VesselDeparture <= PeriodEnd
                                ) > 0
                            THEN (
                                SELECT Top 1 B.Currency
                                FROM MPLPS AS A
                                LEFT JOIN M_Currency AS B ON A.Currency = B.DocEntry
                                WHERE A.TenantKey=T0.TenantId AND A.Type ='Tenant'
                                AND T4.VesselDeparture >= A.PeriodStart AND T4.VesselDeparture <= A.PeriodEnd

                            )
                        ELSE
                            'USD'
                    END AS CurrencyPortService
                ")
                ->where('T0.DocEntry', '=', $doc_entry)
                ->first();



            $template_surat = new TemplateProcessor(public_path('template/nota_penagihan_imip.docx'));
            $pathToSavingDirectoryDocx = public_path('zip/' . Auth::user()->id . '/doc_cleaning/');
            if (!file_exists($pathToSavingDirectoryDocx)) {
                if (!mkdir($pathToSavingDirectoryDocx, 0777, true) && !is_dir($pathToSavingDirectoryDocx)) {
                    throw new \RuntimeException(
                        sprintf(
                            'Directory "%s" was not created',
                            $pathToSavingDirectoryDocx
                        )
                    );
                }
            }

            // info("billing note date: " . $doc->BillingNoteDate);
            if (empty($doc->BillingNoteDate)) {
                throw new \App\Exceptions\CustomException("Billing note date cannot empty!");
            }


            $file_path_name = public_path(
                'zip/' . Auth::user()->id . '/doc_cleaning/' . strtoupper(Str::slug($doc->ChargeTo)) . '.docx'
            );

            $template_surat->setValue('CURR', 'IDR');
            $template_surat->setValue('BILL_TO', str_replace('&', '&amp;', $doc->ChargeTo));
            $template_surat->setValue('NO_NOTA', $doc->DockCleaningNo);
            $template_surat->setValue('DATE', date('d-F-Y', strtotime($doc->BillingNoteDate)));
            $template_surat->setValue('DUE_DATE', date('d-F-Y', strtotime($doc->BillingNoteDate)));
            $template_surat->setValue('ADDRESS', ($doc->AddressTenant) ? str_replace('&', ' &amp; ', $doc->AddressTenant) : str_replace('&', ' &amp; ', $doc->AddressBp));

            $sub_total = 0;
            $data_letter = [];

            $periode_month = date('F', strtotime($doc->BillingNoteDate));
            $periode_year = date('Y', strtotime($doc->BillingNoteDate));

            $qty = floatval($doc->QtyEstimate);
            $qty_revised = floatval($doc->QtyRevised);
            $price = floatval(0.171);
            $price_revised = floatval($doc->PriceRevised);
            // $tax_code = $doc->TaxCode;
            $tax_code = 12;
            $departure = $doc->BillingNoteDate;

            // check qty
            $qty = ($qty_revised == 0) ? $qty : $qty_revised;

            $departure = date('Y-m-d', strtotime($departure));
            $idrPrice = $this->getLatestBiRate("USD", $departure);

            // throw new \App\Exceptions\CustomException(round(round(($qty * $price), 2)), 1);
            $total = round(round(($qty * $price), 2) * $idrPrice);
            $total_with_tax = round(round($total * 11 / 12, 2) * ($tax_code / 100));

            $total_revised = $qty_revised * ($price_revised * $idrPrice);
            $total_with_tax_revised = $tax_code * $total_revised / 100;

            $sub_total += $total;

            // Initialize description
            $desciption = 'Jasa Kebersihan Dermaga ' . $doc->VesselName
                . ' &#10;' . number_format($qty, 4) . ' MT &#10; $' . number_format($price, 3) . ' x Rp. ' . number_format($idrPrice, 2);

            // Check if sub_total < 50000 and handle multiple records logic
            if ($sub_total < 50000) {
                // Check if there are multiple records with same ChargeTo, DocNum, and Type
                $same_records = DB::table('BEXP')
                    ->where('ChargeTo', $doc->ChargeTo)
                    ->where('DocNum', $doc->DocNum)
                    ->where('Type', $billing_type)
                    ->get();

                $record_count = $same_records->count();

                if ($record_count == 1) {
                    // If only 1 record, make sub_total = 50000
                    $sub_total = 50000;
                    $total = 50000;
                    $total_with_tax = round(round($total * 11 / 12, 2) * ($tax_code / 100));
                } else if ($record_count > 1) {
                    // If multiple records, check sum of sub_total
                    $sum_sub_total = $same_records->sum('DockCleaningAmount');

                    if ($sum_sub_total < 50000) {
                        // If sum < 50000, update description to include qty and sum total
                        $total_qty = $same_records->sum(function ($record) {
                            $qty_est = floatval($record->QtyEstimate);
                            $qty_rev = floatval($record->QtyRevised);
                            return ($qty_rev == 0) ? $qty_est : $qty_rev;
                        });

                        $desciption = 'Jasa Kebersihan Dermaga ' . $doc->VesselName
                            . ' &#10;' . number_format($total_qty, 4) . ' MT (Combined) &#10; $' . number_format($price, 3) . ' x Rp. ' . number_format($idrPrice, 2);

                        // Update total to 50000
                        $sub_total = 50000;
                        $total = 50000;
                        $total_with_tax = round(round($total * 11 / 12, 2) * ($tax_code / 100));
                    }
                    // If sum >= 50000, keep normal calculation
                }
            }


            $data_letter[] = [
                'NO' => 1,
                'DESCRIPTION' => $desciption,
                'AMOUNT' => number_format($total, 2),
                'SUB_TOTAL' => $sub_total,
                'VAT' => $total_with_tax,
                'TOTAL' => $total_with_tax + $total,
            ];

            $grantTotal = ($total_with_tax + $total);

            $template_surat->setValue('SUB_TOTAL', number_format($sub_total, 2));
            $template_surat->setValue('VAT', number_format($total_with_tax, 2));
            $template_surat->setValue('TOTAL', number_format($grantTotal, 2));

            $template_surat->cloneRowAndSetValues('NO', $data_letter);

            $template_surat->saveAs($file_path_name);

            RemoveAttachment::dispatch($file_path_name)->delay(now()->addMinutes(5));

            $serviceConvert = new ConvertDocxToPdfService();
            $pathToSavingDirectory = public_path('zip/' . Auth::user()->id . '/doc_cleaning/');
            $pdfFileName = strtoupper(Str::slug($doc->ChargeTo)) . '.pdf';
            $serviceConvert->convert($file_path_name, $pathToSavingDirectory, $pdfFileName);

            RemoveAttachment::dispatch($pathToSavingDirectory . $pdfFileName)->delay(now()->addMinutes(10));

            DB::table('BEXP')
                ->where('DocEntry', '=', $doc_entry)
                ->update([
                    'DockCleaningNo' => $doc->DockCleaningNo,
                    'DockCleaningPrice' => $price,
                    'DockCleaningAmount' => $sub_total,
                    'DockCleaningVat' => $total_with_tax,
                    'DockCleaningGrantTotal' => $grantTotal,
                    'DockCleaningUsdIdrConversion' => $idrPrice,
                ]);

            $exhangeRate = ExchangeRate::where("RateDate", "=", $departure)->first();
            if ($exhangeRate) {
                if (empty($exhangeRate->IdrPriceBi)) {
                    $exhangeRate->update([
                        "IdrPriceBi" => $idrPrice
                    ]);
                }
            } else {
                ExchangeRate::create([
                    'RateDate' => $departure,
                    'IdrPrice' => $idrPrice,
                    'IdrPriceBi' => $idrPrice,
                    'CreatedBy' => $request->user()->id,
                    'created_at' => Carbon::now(),
                ]);
            }


            return '/zip/' . Auth::user()->id . '/doc_cleaning/' . $pdfFileName;
        } catch (\Exception $e) {
            throw new CustomException($e->getMessage());
        }
    }

    protected function notaPenagihan($doc_entry, $request)
    {
        $doc = DB::table('BEXP AS T0')
            ->leftJoin('M_Tenant AS T1', 'T0.ChargeTo', 'T1.FullName')
            ->leftJoin('M_BP AS T2', 'T0.ChargeTo', 'T2.Name')
            ->leftJoin('BHLOCAL AS T3', 'T0.DocNum', 'T3.DocEntry')
            ->leftJoin('L_Master AS T4', 'T3.LocalID', 'T4.DocEntry')
            ->leftJoin('M_CARGO AS T5', 'T4.VesselName', 'T5.DocEntry')
            ->selectRaw("
                        IIF(T4.VesselType = 'MV', CONCAT(T5.Name, ' V. ', T4.Voyage),
                            CONCAT(T5.Name, ' ', (select name from M_CARGO Z where Z.DocEntry = T4.tongkang ))) AS VesselName,
                        T1.Address AS AddressTenant,
                        T2.Address As AddressBp,
                        T0.ChargeTo,
                        T0.NoNota,
                        T0.NoNotaSl,
                        T0.ServiceLoading,
                        T3.PeriodDate,
                        T3.BillingNoteDate,
                        T3.Type,
                        T4.VesselDeparture,
                        T0.PriceEstimate,
                        T0.LoadingUnloadingType,
                        T0.PriceRevised,
                        T0.QtyEstimate,
                        T0.QtyRevised,
                        T0.TaxCode,
                        T0.CompanyHeader,
                        T0.Status,
                        T0.DocEntry,
                        CASE
                            WHEN (SELECT COUNT(*) FROM MPLPS
                                    WHERE TenantKey = T0.TenantId AND Type ='Tenant'
                                    AND T4.VesselDeparture >= PeriodStart AND T4.VesselDeparture <= PeriodEnd
                                    ) > 0
                                THEN (
                                    SELECT Top 1 B.Currency
                                    FROM MPLPS AS A
                                    LEFT JOIN M_Currency AS B ON A.Currency = B.DocEntry
                                    WHERE A.TenantKey=T0.TenantId AND A.Type ='Tenant'
                                    AND T4.VesselDeparture >= A.PeriodStart AND T4.VesselDeparture <= A.PeriodEnd

                                )
                            ELSE
                                'USD'
                        END AS CurrencyPortService
                    ")
            ->where('T0.DocEntry', '=', $doc_entry)
            ->where('T3.Status', '<>', 'Cancel')
            ->first();
        // return response()->json($doc, 422);

        if (!file_exists(public_path('zip/' . Auth::user()->id . '/nota_penagihan/'))) {
            mkdir(public_path('zip/' . Auth::user()->id . '/nota_penagihan/'), 0700, true);
        }

        $all_pdf = [];
        if ($doc->LoadingUnloadingType != 'No') {
            // Service loading
            $template_surat = new TemplateProcessor(public_path('template/nota_penagihan_imip.docx'));

            $file_path_name = public_path(
                'zip/' . Auth::user()->id . '/nota_penagihan/' . strtoupper(Str::slug($doc->ChargeTo)) . '_sl' . '.docx'
            );
            $template_surat->setValue('CURR', 'IDR');
            $template_surat->setValue('BILL_TO', str_replace('&', '&amp;', $doc->ChargeTo));
            $template_surat->setValue('NO_NOTA', $doc->NoNotaSl);
            if ($doc->DocEntry == 39116) {
                $template_surat->setValue('DATE', date('d-F-Y', strtotime(date('Y-m-d'))));

                $template_surat->setValue('DUE_DATE', date('d-F-Y', strtotime(date('Y-m-d'))));
            } else {
                $template_surat->setValue('DATE', date('d-F-Y', strtotime($doc->BillingNoteDate)));

                $template_surat->setValue('DUE_DATE', date('d-F-Y', strtotime($doc->BillingNoteDate)));
            }
            $template_surat->setValue('ADDRESS', ($doc->AddressTenant) ? str_replace('&', ' &amp; ', $doc->AddressTenant) : str_replace('&', ' &amp; ', $doc->AddressBp));

            $sub_total = 0;
            $data_letter = [];

            $periode_month = date('F', strtotime($doc->BillingNoteDate));
            $periode_year = date('Y', strtotime($doc->BillingNoteDate));

            $qty = floatval($doc->QtyEstimate);
            $qty_revised = floatval($doc->QtyRevised);
            $price = floatval($doc->ServiceLoading);
            // $price = floatval(10000);
            $price_revised = floatval($doc->PriceRevised);
            // $tax_code = $doc->TaxCode;
            $tax_code = 12;
            $departure = $doc->BillingNoteDate;

            $qty = ($qty_revised == 0) ? $qty : $qty_revised;

            $departure = date('Y-m-d', strtotime($departure));
            $rate = ExchangeRate::where('RateDate', '=', $departure)->first();
            $total = round($qty * ($price));

            // 001-IMIP-JKT-I-25 PEMBERLAKUAN HARGA MINIMUM(1).pdf
            // $total = ($total < 50000) ? 50000 : $total;

            // $total_with_tax = round($tax_code * $total / 100);
            $total_with_tax = round(round($total * 11 / 12, 2) * ($tax_code / 100));

            $total_revised = $qty_revised * ($price_revised);
            $total_with_tax_revised = $tax_code * $total_revised / 100;

            $total = $total;
            $total_with_tax = $total_with_tax;

            $sub_total += $total;
            $desciption = 'Jasa Pembongkaran dan Pengangkutan ' . $doc->VesselName
                . '&#10; ' . number_format($qty, 4) . ' MT x Rp. ' . number_format($price, 2);

            $data_letter[] = [
                'NO' => 1,
                'DESCRIPTION' => $desciption,
                'AMOUNT' => number_format($total, 2),
                'SUB_TOTAL' => $sub_total,
                'VAT' => $total_with_tax,
                'TOTAL' => $total_with_tax + $total,
            ];

            $template_surat->setValue('SUB_TOTAL', number_format($sub_total, 2));
            $template_surat->setValue('VAT', number_format($total_with_tax, 2));
            $template_surat->setValue('TOTAL', number_format(($total_with_tax + $total), 2));

            $template_surat->cloneRowAndSetValues('NO', $data_letter);

            $template_surat->saveAs($file_path_name);

            RemoveAttachment::dispatch($file_path_name)->delay(now()->addMinutes(5));

            $serviceConvert = new ConvertDocxToPdfService();
            $pathToSavingDirectory = public_path('zip/' . Auth::user()->id . '/nota_penagihan/');
            $pdfFileName = strtoupper(Str::slug($doc->ChargeTo)) . '_sl' . '.pdf';
            $serviceConvert->convert($file_path_name, $pathToSavingDirectory, $pdfFileName);

            RemoveAttachment::dispatch($pathToSavingDirectory . $pdfFileName)->delay(now()->addMinutes(10));

            $all_pdf[] = $pathToSavingDirectory . $pdfFileName;
            // End Service loading
        }

        // Port service
        if ($doc->CompanyHeader == 'BDT') {
            $template_surat = new TemplateProcessor(public_path('template/nota_penagihan_ps_bdt.docx'));
        } else {
            $template_surat = new TemplateProcessor(public_path('template/nota_penagihan_ps_imip.docx'));
        }

        $file_path_name = public_path(
            'zip/' . Auth::user()->id . '/nota_penagihan/' . strtoupper(Str::slug($doc->ChargeTo)) . '_ps' . '.docx'
        );

        $template_surat->setValue('CURR', 'IDR');
        $template_surat->setValue('BILL_TO', str_replace('&', '&amp;', $doc->ChargeTo));
        $template_surat->setValue('NO_NOTA', $doc->NoNota);
        $template_surat->setValue('DATE', date('d-F-Y', strtotime($doc->BillingNoteDate)));
        $template_surat->setValue('DUE_DATE', date('d-F-Y', strtotime($doc->BillingNoteDate)));
        $template_surat->setValue('ADDRESS', ($doc->AddressTenant) ? str_replace('&', ' &amp; ', $doc->AddressTenant) : str_replace('&', ' &amp; ', $doc->AddressBp));

        $sub_total = 0;
        $data_letter = [];

        $periode_month = date('F', strtotime($doc->BillingNoteDate));
        $periode_year = date('Y', strtotime($doc->BillingNoteDate));

        $qty = floatval($doc->QtyEstimate);
        $qty_revised = floatval($doc->QtyRevised);
        $price = floatval($doc->PriceEstimate);
        $price_revised = floatval($doc->PriceRevised);
        // $tax_code = $doc->TaxCode;
        $tax_code = 12;
        $departure = $doc->BillingNoteDate;

        // check qty
        $qty = ($qty_revised == 0) ? $qty : $qty_revised;

        $departure = date('Y-m-d', strtotime($departure));
        $rate = ExchangeRate::where('RateDate', '=', $departure)->first();
        // throw new \App\Exceptions\CustomException(round(round(($qty * $price), 2)), 1);
        $total = round(round(($qty * $price), 2) * $rate->IdrPrice);
        // $total = round(round(($qty * $price), 4) * $rate->IdrPrice);
        // $total = round(($qty * $price), 2) * $rate->IdrPrice;
        // throw new \App\Exceptions\CustomException(round(($qty * $price), 2), 1);
        // throw new \App\Exceptions\CustomException($total, 1);
        // $total = ($doc->DocEntry == 57931) ? ($total + 1) : $total;

        // 001-IMIP-JKT-I-25 PEMBERLAKUAN HARGA MINIMUM(1).pdf
        // $total = ($total < 50000) ? 50000 : $total;
        // info("total with tax code" . ($tax_code * $total / 100));
        // $total_with_tax = round($tax_code * $total / 100);
        $total_with_tax = round(round($total * 11 / 12, 2) * ($tax_code / 100));

        $total_revised = $qty_revised * ($price_revised * $rate->IdrPrice);
        $total_with_tax_revised = $tax_code * $total_revised / 100;

        $sub_total += $total;
        $desciption = 'Rental Space Jetty ' . $doc->VesselName
            . ' &#10;' . number_format($qty, 4) . ' MT &#10; $' . number_format($price, 2) . ' x Rp. ' . number_format($rate->IdrPrice, 2);


        // Log::info('total pranota port service', [
        //     'qty' => $qty,
        //     'price' => $price,
        //     'rate' => $rate->IdrPrice,
        //     'total' => $total,
        //     'round price x qty' => round(($qty * $price), 2)
        // ]);

        $data_letter[] = [
            'NO' => 1,
            'DESCRIPTION' => $desciption,
            'AMOUNT' => number_format($total, 2),
            'SUB_TOTAL' => $sub_total,
            'VAT' => $total_with_tax,
            'TOTAL' => $total_with_tax + $total,
        ];

        $template_surat->setValue('SUB_TOTAL', number_format($sub_total, 2));
        $template_surat->setValue('VAT', number_format($total_with_tax, 2));
        $template_surat->setValue('TOTAL', number_format(($total_with_tax + $total), 2));

        $template_surat->cloneRowAndSetValues('NO', $data_letter);

        $template_surat->saveAs($file_path_name);

        RemoveAttachment::dispatch($file_path_name)->delay(now()->addMinutes(5));

        $serviceConvert = new ConvertDocxToPdfService();
        $pathToSavingDirectory = public_path('zip/' . Auth::user()->id . '/nota_penagihan/');
        $pdfFileName = strtoupper(Str::slug($doc->ChargeTo)) . '_ps' . '.pdf';
        $serviceConvert->convert($file_path_name, $pathToSavingDirectory, $pdfFileName);

        RemoveAttachment::dispatch($pathToSavingDirectory . $pdfFileName)->delay(now()->addMinutes(10));

        $all_pdf[] = $pathToSavingDirectory . $pdfFileName;

        // End Port Service

        $zipper = new Zipper();
        $zip_path = public_path('zip/' . $request->user()->id . "/BILLING/{$doc->Type}/");
        if (!file_exists($zip_path)) {
            if (!mkdir($zip_path, 0777, true) && !is_dir($zip_path)) {
                throw new \RuntimeException(
                    sprintf(
                        'Directory "%s" was not created',
                        $zip_path
                    )
                );
            }
        }

        try {
            $data_file = $zip_path . 'nota_penagihan.zip';
            if (file_exists($data_file)) {
                unlink($data_file);
            } else {
                fopen($data_file, "w");
            }
            $zipper->make($data_file)->add($all_pdf);
            $zipper->close();
            $response = 'zip/' . $request->user()->id . "/BILLING/{$doc->Type}/nota_penagihan.zip";

            RemoveAttachment::dispatch($data_file)->delay(now()->addMinutes(5));

            return $response;
        } catch (\Exception $e) {
            throw new \App\Exceptions\CustomException($e->getMessage());
        }
    }

    /**
     * @param $items
     *
     * @return mixed|null
     */
    protected function storeClassification($items, $type)
    {
        if (array_key_exists('Classification', $items)) {
            $class_name = $items['Classification'];
            $classification = Classification::firstOrCreate([
                'Name' => $class_name,
                //"ReportType" => $type
            ]);
            return $class_name;
        }
        return null;
    }

    /**
     * @param $date
     * @param $desc
     *
     * @return string
     */
    protected function returnLocalDate($date, $desc)
    {
        return Carbon::parse($date)->isoFormat('Do MMMM YYYY') . ' PUKUL ' .
            Carbon::parse($date)->isoFormat('H.mm') . ' WITA ' . ($desc ? "(${desc})" : '');
    }
}
