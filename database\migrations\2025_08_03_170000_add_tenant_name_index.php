<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('M_Tenant', function (Blueprint $table) {
            $table->index('Name');
        });
        
        // Add composite indexes for frequently used date range queries
        Schema::table('MPLPS', function (Blueprint $table) {
            $table->index(['PeriodStart', 'PeriodEnd']);
        });
        
        Schema::table('MPLSL', function (Blueprint $table) {
            $table->index(['PeriodStart', 'PeriodEnd']);
        });
        
        // Add index for BEXP DocEntry lookups
        Schema::table('BEXP', function (Blueprint $table) {
            $table->index('DocEntry');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('M_Tenant', function (Blueprint $table) {
            $table->dropIndex(['Name']);
        });
        
        Schema::table('MPLPS', function (Blueprint $table) {
            $table->dropIndex(['PeriodStart', 'PeriodEnd']);
        });
        
        Schema::table('MPLSL', function (Blueprint $table) {
            $table->dropIndex(['PeriodStart', 'PeriodEnd']);
        });
        
        Schema::table('BEXP', function (Blueprint $table) {
            $table->dropIndex(['DocEntry']);
        });
    }
};
