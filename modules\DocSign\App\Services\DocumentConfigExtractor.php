<?php

namespace Modules\DocSign\App\Services;

use App\Jobs\RemoveAttachment;
use Mo<PERSON>les\Master\App\Models\TenantDocumentConfig;
use App\Services\ConvertDocxToPdfService;
use PhpOffice\PhpWord\IOFactory;

class DocumentConfigExtractor
{
    /**
     * Merge two DOCX files into a single PDF file.
     *
     * @param string $templateDocxPath The path to the template DOCX file.
     * @param string $contentDocxPath The path to the content DOCX file.
     * @param string $outputPath The path to the output PDF file.
     * @return string The path to the merged PDF file.
     */
    public function mergePdfs(string $templateDocxPath, string $contentDocxPath, string $outputPath): string
    {
        try {
            $serviceConvert = new ConvertDocxToPdfService();
            $pathToSavingDirectory = public_path('docs/Extracted/');

            if (!file_exists($pathToSavingDirectory)) {
                if (!mkdir($pathToSavingDirectory, 0777, true) && !is_dir($pathToSavingDirectory)) {
                    throw new \RuntimeException(
                        sprintf(
                            'Directory "%s" was not created',
                            $pathToSavingDirectory
                        )
                    );
                }
            }

            if (!is_file($templateDocxPath)) {
                throw new \App\Exceptions\CustomException("Template file not found or is not a valid file " . $templateDocxPath);
            }

            // Convert template to PDF (background)
            $pdfFileName = pathinfo($templateDocxPath, PATHINFO_FILENAME) . ".pdf";
            $serviceConvert->convert($templateDocxPath, $pathToSavingDirectory, $pdfFileName);
            $baseTemplatePdf = $pathToSavingDirectory . $pdfFileName;

            // Convert content document to PDF (foreground)
            $pdfFileNameContent = pathinfo($contentDocxPath, PATHINFO_FILENAME) . ".pdf";
            $serviceConvert->convert($contentDocxPath, $pathToSavingDirectory, $pdfFileNameContent);
            $contentPdf = $pathToSavingDirectory . $pdfFileNameContent;

            // Create PDF with import capability
            $pdf = new \setasign\Fpdi\Tcpdf\Fpdi();
            $pdf->setPrintHeader(false);
            $pdf->setPrintFooter(false);

            // Import content pages
            $contentPages = $pdf->setSourceFile($contentPdf);

            // For each content page
            for ($pageNo = 1; $pageNo <= $contentPages; $pageNo++) {
                // Add blank page
                $pdf->AddPage();

                // Import and use template (first page always)
                $pdf->setSourceFile($baseTemplatePdf);
                $tplIdx = $pdf->importPage(1);
                $pdf->useTemplate($tplIdx);

                // Import and use content page
                $pdf->setSourceFile($contentPdf);
                $contentIdx = $pdf->importPage($pageNo);
                $pdf->useTemplate($contentIdx);
            }

            // Save merged PDF
            $pdf->Output($outputPath, 'F');

            // Clean up temporary files
            RemoveAttachment::dispatch($baseTemplatePdf)->delay(now()->addMinutes(15));
            RemoveAttachment::dispatch($contentPdf)->delay(now()->addMinutes(15));
            RemoveAttachment::dispatch($outputPath)->delay(now()->addMinutes(15));
            return $outputPath;

        } catch (\Exception $e) {
            // Clean up any temporary files
            throw $e;
        }
    }

    private function convertDocxToPdf($docxPath)
    {
        try {
            // Load DOCX
            $phpWord = IOFactory::load($docxPath);

            // Create temporary file for PDF
            $pdfPath = tempnam(sys_get_temp_dir(), 'docx_pdf_');

            // Save as PDF using TCPDF
            $pdfWriter = IOFactory::createWriter($phpWord, 'PDF');
            $pdfWriter->save($pdfPath);

            return $pdfPath;

        } catch (\Exception $e) {
            \Log::error('Error converting DOCX to PDF: ' . $e->getMessage());
            throw $e;
        }
    }

    private function getPageCount($pdfPath)
    {
        $pdfText = file_get_contents($pdfPath);
        preg_match_all("/\/Page\W/", $pdfText, $matches);
        return count($matches[0]);
    }

    public function saveToDatabase($config, $tenantId, $documentType)
    {
        return TenantDocumentConfig::create([
            'TenantId' => $tenantId,
            'DocumentType' => $documentType,

            // Header configuration
            'HeaderImage' => $config['header']['image'] ?? '',
            'HeaderImageHeight' => $config['header']['height'] ?? 0,
            'HeaderImageWidth' => $config['header']['width'] ?? 0,
            'HeaderPosition' => $config['header']['positioning'] ?? 'absolute',
            'HeaderHorizontalPos' => $config['header']['alignment'] ?? 'center',
            'HeaderVerticalPos' => 'top',
            'HeaderMarginTop' => 0,
            'HeaderMarginLeft' => 0,

            // Footer configuration
            'FooterImage' => $config['footer']['image'] ?? '',
            'FooterImageHeight' => $config['footer']['height'] ?? 0,
            'FooterImageWidth' => $config['footer']['width'] ?? 0,
            'FooterPosition' => $config['footer']['positioning'] ?? 'absolute',
            'FooterHorizontalPos' => $config['footer']['alignment'] ?? 'center',
            'FooterVerticalPos' => 'bottom',
            'FooterMarginBottom' => 0,
            'FooterMarginLeft' => 0,

            // Watermark configuration
            'WatermarkImage' => $config['watermark']['image'] ?? '',
            'WatermarkImageHeight' => $config['watermark']['height'] ?? 0,
            'WatermarkImageWidth' => $config['watermark']['width'] ?? 0,
            'WatermarkPosition' => $config['watermark']['positioning'] ?? 'absolute',
            'WatermarkHorizontalPos' => 'center',
            'WatermarkVerticalPos' => 'center',
            'WatermarkWrappingStyle' => 'behind',
            'WatermarkMarginTop' => 0,
            'WatermarkMarginLeft' => 0,
            'WatermarkOpacity' => 50,
        ]);
    }
}
