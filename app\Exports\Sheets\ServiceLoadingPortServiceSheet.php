<?php


namespace App\Exports\Sheets;

use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Events\BeforeSheet;
use Maatwebsite\Excel\Sheet;

class ServiceLoadingPortServiceSheet implements FromView, ShouldAutoSize, WithEvents, WithTitle
{
    protected $first_date;
    protected $second_date;
    protected $report_type;
    protected $preview_reports;
    protected $index;
    private $company_header;
    private $currency;

    /**
     * ServiceLoadingPortServiceSheet constructor.
     * @param $first_date
     * @param $second_date
     * @param $report_type
     * @param $preview_reports
     * @param $index
     * @param $company_header
     * @param $currency
     */
    public function __construct(
        $first_date,
        $second_date,
        $report_type,
        $preview_reports,
        $index,
        $company_header,
        $currency
    )
    {
        $this->first_date = $first_date;
        $this->second_date = $second_date;
        $this->report_type = $report_type;
        $this->preview_reports = $preview_reports;
        $this->index = $index;
        $this->company_header = $company_header;
        $this->currency = $currency;
    }

    /**
     * @return View
     */
    public function view(): View
    {
        return view(
            'export.excel.serviceLoadingPortService',
            [
                'summaries' => $this->preview_reports,
                'first_date' => $this->first_date,
                'second_date' => $this->second_date,
                'report_type' => $this->report_type,
                'companyHeader' => $this->company_header,
                'currency' => $this->currency,
            ]
        );
    }

    /**
     * @return array|\Closure[]
     */
    public function registerEvents(): array
    {
        return [
            BeforeSheet::class => function (BeforeSheet $event) {
                Sheet::macro('styleCells', function (Sheet $sheet, string $cellRange, array $style) {
                    $sheet->getDelegate()->getStyle($cellRange)->applyFromArray($style);
                });
            }
        ];
    }

    /**
     * @return string
     */
    public function title(): string
    {
        switch ($this->index) {
            case 1:
                return 'LOADING EXPORT CARGO';
                break;

        }
    }
}
