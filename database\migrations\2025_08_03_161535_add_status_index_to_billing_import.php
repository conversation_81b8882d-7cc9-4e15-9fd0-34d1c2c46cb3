<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('BHIMP', function (Blueprint $table) {
            $table->index('ImportID');
            $table->index('BillingNoteDate');
            $table->index('PeriodDate');
            $table->index('Jetty');
            $table->index('PostingDate');
        });
        Schema::table('BHEXP', function (Blueprint $table) {
            $table->index('ExportID');
            $table->index('BillingNoteDate');
            $table->index('PeriodDate');
            $table->index('Jetty');
            $table->index('PostingDate');
        });
        Schema::table('BHLOCAL', function (Blueprint $table) {
            $table->index('LocalID');
            $table->index('BillingNoteDate');
            $table->index('PeriodDate');
            $table->index('Jetty');
            $table->index('PostingDate');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('billing_import', function (Blueprint $table) {
            //
        });
    }
};
