<?php

namespace App\Exports;

use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Events\AfterSheet;
use Maatwebsite\Excel\Events\BeforeSheet;
use Maatwebsite\Excel\Sheet;


class RecapImportExport implements FromView, ShouldAutoSize, WithEvents, WithColumnFormatting
{
    public $data;
    public $vesselName;
    public $voyage;
    public $reportType;
    public function __construct($data, $vesselName, $voyage, $reportType)
    {
        $this->data = $data;
        $this->vesselName = $vesselName;
        $this->voyage = $voyage;
        $this->reportType = $reportType;
    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event) {
                $cellRange = 'A1:W1'; // All headers
                $event->sheet->getDelegate()->getStyle($cellRange)->getFont()->setSize(12);
            },
            BeforeSheet::class => function (BeforeSheet $event) {
                Sheet::macro('styleCells', function (Sheet $sheet, string $cellRange, array $style) {
                    $sheet->getDelegate()->getStyle($cellRange)->applyFromArray($style);
                });
            }
        ];
    }

    public function columnFormats(): array
    {
        return [
            // 'AE' => '0.0', // Assuming column A
            // Add more column formats as needed
        ];
    }

    /**
     * @return View
     */
    public function view(): View
    {
        if ($this->reportType == 'Rekap BM By Date Detail') {
            $view = 'export.recap_bm_detail';
        } elseif (str($this->reportType)->contains(['Rekap BM By Voyage', 'Rekap BM By Date'])) {
            $view = 'export.recap_bm';
        } else {
            $view = 'export.recap';
        }
        // $view = (str($this->reportType)->contains(['Voyage', 'Shipment'])) ? 'export.recap' : 'export.recap_bm';
        return view(
            $view,
            [
                'data' => $this->data,
                'vesselName' => $this->vesselName,
                'voyage' => $this->voyage,
            ]
        );
    }
}
