<?php

namespace App\Console\Commands;

use App\Traits\AppConfig;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Http;

class CheckDocumentStatusPrivy extends Command
{
    use AppConfig;
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:status-privy';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check Batch Approval Status';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        $username = Crypt::decryptString($this->getConfigByName('BackendUsername', 'BACKEND'));
        $password = Crypt::decryptString($this->getConfigByName('BackendPassword', 'BACKEND'));
        $url = $this->getConfigByName('BackendUrl', 'BACKEND');
        $login = Http::withoutVerifying()
            ->timeout(90)
            ->retry(3, 100)
            ->withOptions(["verify" => false])
            ->post($url . '/api/auth/login', [
                'username' => $username,
                'password' => $password,
            ]);

        if ($login->successful()) {
            $response = Http::withoutVerifying()
                ->timeout(90)
                ->retry(3, 100)
                ->withOptions(["verify" => false])
                ->withToken($login->json()['token'])
                ->get($url . '/api/status-batch-ekb/0');
            dd($response->json());
        }
    }
}
