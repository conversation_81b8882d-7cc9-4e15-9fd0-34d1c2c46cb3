<?php

namespace App\Http\Controllers\Transact;

use App\Events\ImportDocumentUpdated;
use App\Http\Controllers\Controller;
use App\Models\Transaction\MonitorDoc;
use App\Models\Transaction\MonitorDocBl;
use App\Models\Transaction\MonitorDocInv;
use App\Models\Transaction\TMDocHeader;
use App\Services\Import\ImportService\ImportService;
use App\Traits\ImportHelper;
use App\Traits\ProcessImportData;
use App\Traits\SAPKBDoc;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Modules\Master\App\Models\UserPpjk;
use PDO;

class ImportController extends Controller
{
    use ImportHelper, SAPKBDoc, ProcessImportData;

    public $service;

    public function __construct()
    {
        $this->middleware(['direct_permission:KB Import-index'])->only(['index', 'importDetails', 'getDocAttachment', 'getESign']);
        $this->middleware(['direct_permission:KB Import-store'])->only(['store', 'storeMedia']);
        $this->middleware(['direct_permission:KB Import-edits'])->only(['update', 'reOpenDocStatus', 'reSendEmailChange']);
        $this->middleware(['direct_permission:KB Import-erase'])->only(['destroy', 'deleteMedia']);

        // $this->service = $service;
    }

    /**
     * Display a listing of the resource.
     *
     * @param Request $request
     *
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        $options = json_decode($request->pagination);
        $year_local = date('Y');
        $filter = isset($request->filter) ? (string) $request->filter : $year_local;
        $pages = isset($options->page) ? (int) $options->page : 1;
        $row_data = ($request->itemsPerPage) ? $request->itemsPerPage : 1000;
        $sorts = isset($request->sortBy[0]) ? json_decode($request->sortBy[0])->key : 'Shipement';
        $order = isset($request->sortBy[0]) ? json_decode($request->sortBy[0])->order : 'ASC';

        // return $this->error($order, 422, [
        //     (isset($options->sortDesc[0]))
        // ]);

        $search = isset($request->q) ? (string) $request->q : '';
        $selectData = isset($request->selectData) ? (string) $request->selectData : 'DocNum';

        $doc_status = isset($request->docStatus) ? (string) $request->docStatus : 'Open';
        $offset = $request->page;
        $row_data = ($request->itemsPerPage) ? $request->itemsPerPage : 1000;
        $username = $request->user()->username;
        $authUser = $request->user();
        $permission_id = $request->user()->permission_id;

        $result = [];
        $query = DB::table('T_MDOC_Header AS T0')
            ->leftJoin('M_Jetty as T1', 'T1.DocEntry', 'T0.Jetty')
            ->distinct()
            ->selectRaw("
                T0.DocEntry
                ,T0.DocNum
                ,T1.Name AS Jetty
                ,T1.Port
                ,(SELECT DocNum FROM BHIMP WHERE ImportID = T0.DocEntry AND Status <> 'Cancel') AS BillingNum
                ,(SELECT Status FROM BHIMP WHERE ImportID = T0.DocEntry AND Status <> 'Cancel') AS BillingStatus
                ,T0.Status
                ,T0.VesselStatus
                ,T0.BP
                ,T0.Cargo
                ,T0.Shipement
                ,T0.Shipement_no
                ,T0.PortOfLoading
                ,T0.DestinationPort
                ,CONVERT(VARCHAR, T0.Vessel_arrive, 106) AS Vessel_arrive
                ,CONVERT(VARCHAR, T0.VesselDeparture, 106) AS VesselDeparture
                ,T0.Created_by
                ,T0.Updated_by
                ,T0.created_at
                ,T0.updated_at
                ,CONVERT(VARCHAR, T0.PostingDate, 106) AS PostingDate
                ,T0.Color
                ,T0.Remarks
                ,T0.Voyage
                ,T0.Type
                ,'actions' as actions
                ,CONCAT(FORMAT( (
                        SELECT SUM(
                        CASE
                            WHEN UPPER(B.UnitWeight) = 'KGS'  THEN ROUND(B.GrossWeight / 1000, 4)
                            WHEN UPPER(B.UnitWeight) = 'KG' THEN ROUND(B.GrossWeight / 1000, 4)
                            ELSE ROUND(B.GrossWeight, 4)
                        END
                        )
                        FROM T_MDOC AS B
                        WHERE B.DocNum = T0.DocEntry
                    ), '#,###,###.###'), ' MT') AS Weight


                ,CASE
                    WHEN (SELECT COUNT(*) FROM T_MDOC_Header X WHERE X.DocEntry IN (
                        SELECT ImportID FROM BHIMP WHERE Status='Open') AND X.DocEntry = T0.DocEntry) > 0 THEN '#1E88E5'
                    WHEN (SELECT COUNT(*) FROM T_MDOC_Header X WHERE X.DocEntry IN (
                        SELECT ImportID FROM BHIMP WHERE Status='Open-upload') AND X.DocEntry = T0.DocEntry) > 0 THEN '#69F0AE'
                    WHEN LEN(T0.VesselDeparture) > 0 THEN '#43A047'
                    ELSE '#FFF'
                END AS ColorUpdate

                ,T0.Flags
                ,LEFT(T0.DocNum, 2) as LeftDocNum
                ,CASE
                    ISNUMERIC(T0.[Shipement])
                    WHEN 1 THEN REPLICATE('0', 100 - LEN(T0.[Shipement])) + T0.[Shipement]
                    ELSE T0.[Shipement]
                END as OrderShipment
            ")
            ->where("T0.DocStatus", "LIKE", "%" . $doc_status . "%")
            // ->where("T0.DocEntry", "=", DB::raw("(CONVERT(NVARCHAR, T2.DocEntry))"))
            ->when($selectData, function ($query) use ($selectData, $search) {
                if ($selectData == 'BL') {
                    $data_query = $query->whereRaw(
                        "T0.DocEntry IN ( SELECT X0.DocNum
                            FROM T_MDOC AS X0
                            WHERE X0.No_bl LIKE '%${search}%'
                        )"
                    );
                }

                if ($selectData == 'Tenant') {
                    $data_query = $query->where("T2.Name", "LIKE", "%$search%");
                }

                if ($selectData == 'Vessel Name') {
                    $data_query = $query->where('T0.Cargo', 'LIKE', '%' . $search . '%');
                }

                if ($selectData == 'Shipment') {
                    $data_query = $query->where('T0.Shipement', 'LIKE', '%' . $search . '%');
                }

                if (
                    $selectData && !Str::contains($selectData, [
                        'BL',
                        'Vessel Name',
                        'Shipment',
                        'Tenant',
                        'No Invoice'
                    ]) && $search
                ) {
                    $data_query = $query->whereRaw("
                        T0.${selectData} LIKE '%${search}%'
                    ");
                }
            })
            ->when($authUser, function ($query) use ($authUser) {
                $username = $authUser->username;
                if ($authUser->hasAnyRole(['Admin Jakarta'])) {
                    $query->leftJoin(DB::raw("(
                        SELECT distinct X1.DocEntry, X3.Name
                        FROM T_MDOC_Header as X1
                        left join T_MDOC as X2 on X1.DocEntry  = X2.DocNum  AND X2.DocType='Import' AND X2.Deleted='N'
                        left join M_Tenant as X3 on X2.Tenant_key = X3.DocEntry
                        WHERE X2.Tenant_key IN ( SELECT X00.DocEntry
                                FROM M_Tenant AS X00
                                LEFT JOIN M_User_tenant AS X11 ON X00.DocEntry = X11.Tenant_key
                                WHERE X11.User_key = (SELECT id FROM users WHERE username = '${username}')
                            )
                    ) as T2"), 'T0.DocEntry', 'T2.DocEntry');
                } else {
                    $query->join(DB::raw("(
                        SELECT distinct X1.DocEntry, X3.Name
                        FROM T_MDOC_Header as X1
                        left join T_MDOC as X2 on X1.DocEntry  = X2.DocNum  AND X2.DocType='Import' AND X2.Deleted='N'
                        left join M_Tenant as X3 on X2.Tenant_key = X3.DocEntry
                        WHERE X2.Tenant_key IN ( SELECT X00.DocEntry
                                FROM M_Tenant AS X00
                                LEFT JOIN M_User_tenant AS X11 ON X00.DocEntry = X11.Tenant_key
                                WHERE X11.User_key = (SELECT id FROM users WHERE username = '${username}')
                            )
                    ) as T2"), 'T0.DocEntry', 'T2.DocEntry');
                }

                if ($authUser->hasAnyRole(['Admin PPJK JKT'])) {
                    if (!$authUser->hasAnyRole(['ADMIN PPJK JKT MASTER'])) {
                        // $query = $query->where("T0.PPJK", "=", $authUser->ppjk_id);
                        $ppjkId = $authUser->ppjk_id;
                        // $query->whereIn(DB::raw("(SELECT PPJK FROM T_MDOC where DocNum = T0.DocEntry and PPJK is not null)"), $authUser->ppjkMaster()->pluck("PpjkId"));
                        // $query->whereIn(DB::raw("(SELECT PPJK FROM T_MDOC where DocNum = T0.DocEntry and PPJK is not null)"), $authUser->ppjkMaster()->pluck("PpjkId"));
                        $pepjk = '(' . $authUser->ppjkMaster()->pluck("PpjkId")->implode(", ") . ')';
                        $query->whereRaw("T0.DocEntry in (SELECT DocNum FROM T_MDOC where PPJK IN $pepjk  and PPJK is not null )");
                    }
                }
            })
            ->when($permission_id, function ($query) use ($selectData, $search, $permission_id, $filter, $sorts, $order, $authUser) {
                if ($filter == 'All') {
                    $data_query = $query->whereRaw(
                        "LEFT(T0.DocNum, 2) LIKE '%%'"
                    );
                } else {
                    $filter = substr($filter, 2, 2);
                    $data_query = $query->whereRaw(
                        "LEFT(T0.DocNum, 2) LIKE '%${filter}%'"
                    );
                }

                if ($permission_id != '1') {
                    if ($permission_id == '7') {
                        $data_query = $query->where('T0.Deleted', '=', 'N')
                            ->where("T0.DocEntry", "=", DB::raw("(CONVERT(NVARCHAR, T2.DocEntry))"));
                    } else {
                        $data_query = $query->where('T0.Deleted', '=', 'N');
                    }
                }

                if ($sorts == 'Shipement') {
                    $data_query = $query->orderBy(DB::raw('LEFT(T0.DocNum, 2)'), 'DESC')
                        ->orderBy(DB::raw("
                                CASE ISNUMERIC(T0.[Shipement])
                                    WHEN 1 THEN REPLICATE('0', 100 - LEN(T0.[Shipement])) + T0.[Shipement]
                                    ELSE T0.[Shipement]
                                END
                            "), 'DESC');
                } else {
                    $data_query = $query->orderBY($sorts, $order);
                }
                return $data_query;
            });
        //->orderBY(DB::raw("CAST(T0.Shipement AS VARCHAR)"), "ASC");

        $result['total'] = $query->count();
        $import = $query
            ->paginate($row_data)
            ->items();

        // $import = $query->skip($offset)
        //     ->take($row_data)
        //     ->get();

        ////        Insert SAP item name
        // $driver = "HDBODBC";
        // $servername = "**********:30015";
        // $username = "SYSTEM";
        // $password = "hanaSmi*8Ultra";

        // $conn = new PDO("odbc:Driver=$driver;ServerNode=$servername; Uid=$username;Pwd=$password;CHAR_AS_UTF8=true;");
        // $this->transformDataInvdetail($conn);

        //  //        Transform data for insert blKey, InvKey, and InvDetailKey
        // $this->transformDataImport();

        // Move attachment file
        // $this->moveAttachmentFile();
        $year_shipments = DB::table('T_MDOC_Header')
            ->selectRaw('LEFT(DocNUm, 2) as YearDocNum')
            ->orderBy(DB::raw('LEFT(DocNUm, 2)'), 'DESC')
            ->distinct()
            ->get();
        $year_arr = [];
        foreach ($year_shipments as $year_shipment) {
            $year_arr[] = [
                'Name' => '20' . $year_shipment->YearDocNum,
            ];
        }

        $status = DB::table('T_MDOC_Header')
            ->distinct()
            ->select("DocStatus")
            ->pluck('DocStatus')
            ->toArray();

        $item_search = [
            'BL',
            'Shipment',
            'DocNum',
            'Vessel Name',
            'Voyage',
            'Tenant',
            'No Invoice'
        ];

        $filter = array_merge([['Name' => 'All']], $year_arr);

        $result = array_merge($result, [
            'rows' => $import,
            'filter' => $filter,
            'status' => $status,
            'selectData' => $selectData,
            'search' => $search,
            'item_search' => $item_search,
            'tenantExternal' => array_values($this->getTenantExternal()),
        ]);
        return response()->json($result);
    }

    /**
     * @return array
     */
    public function getTenantExternal()
    {
        return DB::table('M_Tenant')
            ->where('Billing', '=', 'CBM')
            ->select('Name')
            ->get()
            ->pluck('Name')
            ->toArray();
    }

    /**
     * @param Request $request
     * @param $docNum
     *
     * @return JsonResponse
     */
    public function importDetails(Request $request, $docNum): JsonResponse
    {
        // $index = $this->getPrevAndNext($docNum, "T_MDOC_Header", "DocEntry");
        $username = $request->user()->username;
        $readonly = isset($request->readOnly) ? (int) $request->readOnly : 2;
        $entry = isset($request->entry) ? (int) $request->entry : -1;

        $query_header = DB::table('T_MDOC_Header AS T0')
            ->leftJoin('M_Tenant AS T1', 'T0.BP', 'T1.Name')
            ->leftJoin('M_Jetty AS T2', 'T2.DocEntry', 'T0.Jetty')
            ->selectRaw("
                T0.*
                ,(SELECT DocNum FROM BHIMP WHERE ImportID = T0.DocEntry AND Status <> 'Cancel') AS BillingNum
                ,(SELECT DocEntry FROM BHIMP WHERE ImportID = T0.DocEntry AND Status <> 'Cancel') AS BillingDocEntry
                ,(SELECT Status FROM BHIMP WHERE ImportID = T0.DocEntry AND Status <> 'Cancel') AS BillingStatus
                ,CONVERT(varchar,T0.Vessel_arrive,20) as Vessel_arrive
                ,CONVERT(varchar,T0.FinishUnloadingDate,20) as FinishUnloadingDate
                ,CONVERT(varchar,T0.UnloadingDate,20) as UnloadingDate
                ,CONVERT(varchar,T0.AnchorageDate,20) as AnchorageDate
                ,CONVERT(varchar,T0.BerthingDate,20) as BerthingDate
                ,CONVERT(varchar,T0.VesselDeparture,20) as VesselDeparture
                ,CONCAT(FORMAT( (
                    SELECT SUM(
                    CASE
				        WHEN UPPER(B.UnitWeight) = 'KGS'  THEN ROUND(B.GrossWeight / 1000, 4)
				        WHEN UPPER(B.UnitWeight) = 'KG' THEN ROUND(B.GrossWeight / 1000, 4)
				        ELSE ROUND(B.GrossWeight, 4)
			        END
                    )
                    FROM T_MDOC AS B
                    WHERE B.DocNum = ${docNum}
                ), '#,###,###.####'), ' MT') AS Weight
                ,CASE
                    WHEN T0.GrossWeight > 0 THEN T0.GrossWeight
                    ELSE
                    (FORMAT( (
                    SELECT SUM(
                    CASE
				        WHEN UPPER(B.UnitWeight) = 'KGS'  THEN ROUND(B.GrossWeight / 1000, 4)
				        WHEN UPPER(B.UnitWeight) = 'KG' THEN ROUND(B.GrossWeight / 1000, 4)
				        ELSE ROUND(B.GrossWeight, 4)
			        END
                    )
                    FROM T_MDOC AS B
                    WHERE B.DocNum = ${docNum}
                    ), '###.####'))
                END AS GrossWeight
                ,T2.Name AS Jetty
                ,T2.Port
                ,ISNULL(T1.Flags, 'N') As NonKB")
            ->whereRaw("T0.DocEntry='${docNum}'")
            ->first();

        $permission_id = Auth::user()->permission_id;
        $user_id = Auth::user()->id;
        $authUser = Auth::user();
        $open_time = str_replace('.', '', microtime(true));
        session(['openDateTime' => $open_time]);

        $query = DB::table('T_MDOC AS T0')
            ->leftJoin('M_TBC AS T1', 'T1.DocEntry', 'T0.BC_type_key')
            ->leftJoin('M_Tenant AS T2', 'T2.DocEntry', 'T0.Tenant_key')
            ->leftJoin('users AS T3', 'T3.id', 'T0.Created_id')
            ->leftJoin('M_Agent AS T4', 'T4.DocEntry', 'T0.Agent')
            ->selectRaw("
                T2.Name AS Tenant_name
                ,T0.BP
                ,(
                    SELECT COUNT(*) FROM MonitorDocWhs AS whs
                    WHERE whs.DocumentId = T0.DocEntry
                ) AS CountWarehouse
                ,null as TenantGroupName
                ,T4.Name AS Agent
                ,(
                    SELECT IsSelected
                    from T_MDOC_BZ
                    where BaseId = T0.DocEntry
                ) as Bc16,
                (
                    SELECT AJU_No
                    from T_MDOC_BZ
                    where BaseId = T0.DocEntry
                ) as AJU_No_16,
                (
                    SELECT REG_No
                    from T_MDOC_BZ
                    where BaseId = T0.DocEntry
                ) as PIB_No_16,
                (
                    SELECT REG_date
                    from T_MDOC_BZ
                    where BaseId = T0.DocEntry
                ) as PIB_date_16,
                (
                    SELECT SPPB_No
                    from T_MDOC_BZ
                    where BaseId = T0.DocEntry
                ) as SPPB_No_16,
                (
                    SELECT SPPB_date
                    from T_MDOC_BZ
                    where BaseId = T0.DocEntry
                ) as SPPB_date_16
                ,(
                    SELECT SPPD_No
                    from T_MDOC_BZ
                    where BaseId = T0.DocEntry
                ) as SPPD_No_16
                ,(
                    SELECT SPPD_date
                    from T_MDOC_BZ
                    where BaseId = T0.DocEntry
                ) as SPPD_date_16
                ,T0.ItemName
                ,T0.InsuranceCurrency
                ,T0.InsuranceValue
                ,T0.LetterNo
                ,T0.UnitQty
                ,CAST(T0.ItemQty AS DECIMAL(20, 2)) as ItemQty
                ,CAST(T0.GrossWeight AS DECIMAL(20, 4)) as GrossWeight
                ,T0.UnitWeight
                ,T0.PortOfLoading
                ,T0.PPJKCodeTemp
                ,T0.PPJK
                ,(SELECT TOP 1 Code FROM M_PPJK WHERE DocEntry=T0.PPJK) AS PPJKCode
                ,T0.EmailToPPJK
                ,T0.No_bl
                ,CONVERT(VARCHAR, T0.Date_bl, 105) AS Date_bl
                ,T0.AJU_No
                ,T0.PIB_No
                ,CONVERT(VARCHAR, T0.PIB_date, 105) AS PIB_date
                ,CONVERT(VARCHAR, T0.Expired_date, 105) AS Expired_date
                ,CONVERT(VARCHAR, T0.Ebilling_date, 105) AS Ebilling_date
                ,T0.Skep
                ,CONVERT(VARCHAR, T0.Skep_date, 105) AS Skep_date
                ,'attachment' AS Attachment
                ,T0.No_inv
                ,T1.Type
                ,T0.SPPB_No
                ,CONVERT(VARCHAR, T0.SPPB_date, 105) AS SPPB_date
                ,T0.SPPD_No
                ,CONVERT(VARCHAR, T0.SPPD_date, 105) AS SPPD_date
                ,T0.Created_by
                ,T0.Ocean_freight
                ,T0.Currency
                ,T0.Ocean
                ,T0.ContainerNo
                ,T0.Freight_value
                ,T0.CBM
                ,T0.Flags
                ,T0.DocEntry
                ,T0.Remarks
                ,T0.Color
                ,T0.isScan
                ,T0.isOriginal
                ,T0.isSend
                ,T0.isFeOri
                ,T0.isFeSend
                ,T0.SecretKey
                ,T0.IsParent
                ,CONVERT(VARCHAR, T0.EmailToBcDate, 105) as EmailToBcDate
                ,CASE
                    WHEN T0.SecretKey IS NULL THEN (
                            SELECT COUNT(*) FROM M_Doc_attachment as x
                            WHERE x.M_doc_key = T0.DocEntry
                            AND x.TransType='ImportDetails'
                        )
                    ELSE (
                            SELECT COUNT(*) FROM M_Doc_attachment as x
                            WHERE x.M_doc_key = T0.DocEntry
                            AND x.TransType='ImportDetails'
                        )
                END AS CountAttachment,
                (
                    SELECT COUNT(*) FROM M_Doc_attachment as x
                            WHERE x.M_doc_key = T0.DocEntry
                            AND x.TransType='ImportDetails'
                            AND X.name like '%surat%'
                ) as CountAttachmentSurat
                ,T0.Status
                ,T2.Flags As NonKB
                ,T0.MatchKey
                ,(
                    SELECT COUNT(*) FROM T_MDOC_bl AS a
                    WHERE a.BlKey = T0.DocEntry
                    AND a.Deleted='N'
                ) AS CountInv
                ,(
                    SELECT top 1
                        CASE
                            WHEN A.ProcessName = '' THEN null
                            ELSE RIGHT(A.ProcessName, CHARINDEX(' ', REVERSE(A.ProcessName)) - 1)

                        END
                     AS LastWord
                    FROM T_MDOC_Process AS A
                    WHERE A.DocumentId = T0.DocEntry
                    AND T0.Deleted='N'
                    ORDER BY DocEntry DESC
                ) AS Confirmation
                ,(
                    SELECT COUNT(*) FROM notuls AS a
                    WHERE a.doc_id = T0.DocEntry
                ) AS CountNotul
                ,T0.LineNum
                ,T0.isChange
                ,'Process' as Process
                ,'Process2' as Process2
                ,T0.DocNum
                ,T0.SPPBStatus
                ,CONVERT(VARCHAR, T0.BillingDate, 105) as BillingDate
                ,T0.Rate
                ,T0.IsUrgent
                ,T0.IncreaseValue
                ,T0.DecreaseValue
                , (SELECT PPN FROM VW_DOC_TRANSACTION_VALUE_V2 WHERE DocEntry = T0.DocEntry) as PPN
                , (SELECT PPNBM FROM VW_DOC_TRANSACTION_VALUE_V2 WHERE DocEntry = T0.DocEntry) as PPNBM
                , (SELECT PPH FROM VW_DOC_TRANSACTION_VALUE_V2 WHERE DocEntry = T0.DocEntry) as PPH
                , (SELECT BM FROM VW_DOC_TRANSACTION_VALUE_V2 WHERE DocEntry = T0.DocEntry) as BM
                , (SELECT BMAD FROM VW_DOC_TRANSACTION_VALUE_V2 WHERE DocEntry = T0.DocEntry) as BMAD
                , (SELECT BMTP FROM VW_DOC_TRANSACTION_VALUE_V2 WHERE DocEntry = T0.DocEntry) as BMTP
                , (
                    SELECT STUFF((
                        SELECT ',' + CAST(exp.DocEntry as VARCHAR)
                        FROM T_MDOC_Ref as ref
                        left join vw_data_temporary_export as exp on exp.DocEntry = ref.RefId
                        WHERE ref.BaseId = T0.DocEntry
                        and ref.DocType = 'TemporaryImport'
                        FOR XML PATH('')
                    ), 1, 1, '')
                ) as TemporaryExportId
                , (
                    SELECT STUFF((
                        SELECT ', ' + CAST(exp.DetailItemName as VARCHAR)
                        FROM T_MDOC_Ref as ref
                        left join vw_data_temporary_export as exp on exp.DocEntry = ref.RefId
                        WHERE ref.BaseId = T0.DocEntry
                        and ref.DocType = 'TemporaryImport'
                        FOR XML PATH('')
                    ), 1, 1, '')
                ) as ExportItemName
            ")
            ->where('T0.DocNum', '=', $docNum)
            ->where('T0.Deleted', '=', 'N')
            // ->where(DB::raw("CAST(T5.BcTypeId as bigint)"), "=", DB::raw("CAST(T0.BC_type_key as bigint)"))
            ->where('T0.DocType', '=', (($query_header) ? $query_header->DocType : 'Import'))
            ->when($authUser, function ($query) use ($authUser, $user_id, $readonly, $username, $entry) {
                if ($entry != -1 && $entry != 0) {
                    $query = $query->where('T0.DocEntry', '=', $entry);
                }
                if ($readonly != 1) {
                    if ($authUser->hasAnyRole(['Admin PPJK JKT'])) {
                        $query = $query->where('T0.Flags', '=', 'PPJK JKT');
                        if (!$authUser->hasAnyRole(['ADMIN PPJK JKT MASTER'])) {
                            // $query = $query->where("T0.PPJK", "=", $authUser->ppjk_id);
                            $query->whereIn("T0.PPJK", $authUser->ppjkMaster()->pluck("PpjkId"));
                            // $query = $query->where("T0.PPJK", "=", $authUser->ppjk_id);
                        }
                    } elseif ($authUser->hasAnyRole(['Admin PPJK'])) {
                        $query = $query->where('T0.Flags', '=', 'PPJK');
                    } elseif ($authUser->hasAnyRole(['Admin Tenant'])) {
                        $query = $query->where('T0.Flags', '=', 'T');
                    }
                    // elseif ($permission_id == 6 && $user_id == 32) {
                    //     return $query->where('T0.Flags', '=', 'J')
                    //         ->whereRaw("(
                    //             T0.Created_id = ${user_id} OR [T0].[Tenant_key] in (1, 2, 3, 4, 5)
                    //         )");
                    // }
                    elseif ($authUser->hasAnyRole(['Admin Jakarta Show All'])) {
                        return $query;
                    } elseif ($authUser->hasAnyRole(['Admin Jakarta']) && !$authUser->hasAnyRole(['Admin Jakarta Master', 'Admin Jakarta View Data'])) {
                        // elseif ($authUser->hasAnyRole(['Admin Jakarta'])) {
                        $query = $query->where('T0.Flags', '=', 'J')
                            ->whereRaw(
                                "T0.Tenant_key IN ( SELECT X0.DocEntry
                                    FROM M_Tenant AS X0
                                    LEFT JOIN M_User_tenant AS X1 ON X0.DocEntry = X1.Tenant_key
                                    WHERE X1.User_key = (SELECT id FROM users WHERE username = '${username}')
                                )"
                            );
                        // ->where('T0.Created_id', '=', $user_id);
                    }
                    return $query->whereRaw(
                        "T0.Tenant_key IN ( SELECT X0.DocEntry
                            FROM M_Tenant AS X0
                            LEFT JOIN M_User_tenant AS X1 ON X0.DocEntry = X1.Tenant_key
                            WHERE X1.User_key = (SELECT id FROM users WHERE username = '${username}')
                        )"
                    );
                } else {
                    if ($authUser->hasAnyRole(['Admin PPJK JKT'])) {
                        if (!$authUser->hasAnyRole(['ADMIN PPJK JKT MASTER'])) {
                            $query->whereIn("T0.PPJK", $authUser->ppjkMaster()->pluck("PpjkId"));
                            // $query = $query->where("T0.PPJK", "=", $authUser->ppjk_id);
                        }
                    }
                    if (!Auth::user()->hasAnyRole(['Admin Jakarta', 'Superuser'])) {
                        return $query->whereRaw(
                            "T0.Tenant_key IN ( SELECT X0.DocEntry
                                FROM M_Tenant AS X0
                                LEFT JOIN M_User_tenant AS X1 ON X0.DocEntry = X1.Tenant_key
                                WHERE X1.User_key = (SELECT id FROM users WHERE username = '${username}')
                            )"
                        );
                        // ->where('T0.Created_id', '=', $user_id);
                    }
                    // else {
                    //     return $query->whereRaw(
                    //         "T0.Tenant_key IN ( SELECT X0.DocEntry
                    //             FROM M_Tenant AS X0
                    //             LEFT JOIN M_User_tenant AS X1 ON X0.DocEntry = X1.Tenant_key
                    //             WHERE X1.User_key = (SELECT id FROM users WHERE username = '${username}')
                    //         )"
                    //     );
    
                    // }
                }

                if ($authUser->hasAnyRole(['Admin Tenant Jakarta']) && !Auth::user()->hasRole('Admin Jakarta Master')) {
                    $query = $query->whereRaw(
                        "T0.Tenant_key IN ( SELECT X0.DocEntry
                            FROM M_Tenant AS X0
                            LEFT JOIN M_User_tenant AS X1 ON X0.DocEntry = X1.Tenant_key
                            WHERE X1.User_key = (SELECT id FROM users WHERE username = '${username}')
                        )"
                    );
                }
                return $query;
            })
            ->orderBy("T0.DocEntry")
            ->get();

        $invoices = [];
        $invoice_details = [];
        $inv_detail_sub = [];

        $open_date_time = str_replace('.', '', microtime(true));

        return response()->json([
            'header' => $query_header,
            'rows' => $query,
            'openDateTime' => $open_date_time,
            'invoiceDetails' => $invoice_details,
            'invoiceDetailsSub' => $inv_detail_sub,
            'invoices' => $invoices,
            'docEntry' => $docNum,
            // "prev" => ($index) ? $index->prev : 0,
            // "next" => ($index) ? $index->next : 0,
            'runCBMValidation' => 'Y',
            'tenantExternal' => array_values($this->getTenantExternal()),
        ]);
    }

    /**
     * @return JsonResponse
     */
    public function sppbStatus()
    {
        return response()->json([
            'rows' => [
                'Hijau',
                'Kuning',
                'Merah',
            ],
        ]);
    }

    /**
     * @param Request $request
     *
     * @return bool|JsonResponse
     */
    public function checkInv(Request $request)
    {
        $doc_entry = $request->docEntry;
        $request_type = $request->type;

        $document = $this->getDocDataByDocEntry($doc_entry);

        if ($request_type == 'T') {
            // from jakarta
            if ($document->Tenant_key == 0) {
                return response()->json([
                    'errors' => true,
                    'message' => 'Tenant is empty! ',
                ]);
            }

            if (empty($document->Date_bl) || empty($document->No_bl)) {
                return response()->json([
                    'errors' => true,
                    'message' => 'No B/L or Date B/L is empty! ',
                ]);
            }

            if (empty($document->BP)) {
                return response()->json([
                    'errors' => true,
                    'message' => 'Shipper is empty! ',
                ]);
            }
        } elseif ($request_type == 'J') {
            // from tenant
            $count_inv = $this->countInvByBl($doc_entry);
            $count_inv_detail = $this->countInvDetailByBl($doc_entry);
            $attachment = $this->countAttachmentByTenantOnInv($doc_entry);
            $contract = $this->countNullContractNoOnInvDetail($doc_entry);

            if ($count_inv < 1 || $count_inv_detail < 1) {
                return response()->json([
                    'errors' => true,
                    'message' => 'Invoice and Invoice Details cannot empty ',
                ]);
            }

            // if ($document->BC_type_key == 0) {
            //     return response()->json([
            //         'errors' => true,
            //         'message' => 'BC Type cannot empty!',
            //     ]);
            // }

            if ($document->BCType == '2.3' && $attachment < 1) {
                return response()->json([
                    'errors' => true,
                    'message' => 'Attachment by User Tenant is empty!',
                ]);
            }

            if ($contract > 0) {
                return response()->json([
                    'errors' => true,
                    'message' => 'Contract No cannot empty!',
                ]);
            }
        } elseif ($request_type == 'PPJK') {
            //from ppjk
            $attachment = $this->countAttachmentByJakartaOnBl($doc_entry);
            if ($attachment < 1) {
                return response()->json([
                    'errors' => true,
                    'message' => 'Attachment from User Jakarta cannot empty!',
                ]);
            }
        }

        return response()->json([
            'errors' => false,
        ]);
    }

    public function closeDocument(Request $request, $docEntry)
    {
        DB::beginTransaction();
        try {
            if ($request->user()->hasAnyRole(['Admin Jakarta'])) {
                $doc = MonitorDoc::where("DocEntry", $docEntry)
                    ->select("Remarks", DB::raw("
                        (
                            SELECT top 1
                                CASE
                                    WHEN A.ProcessName = '' THEN null
                                    ELSE RIGHT(A.ProcessName, CHARINDEX(' ', REVERSE(A.ProcessName)) - 1)

                                END
                             AS LastWord
                            FROM T_MDOC_Process AS A
                            WHERE A.DocumentId = T_MDOC.DocEntry
                            AND T_MDOC.Deleted='N'
                            ORDER BY DocEntry DESC
                        ) as Confirmation
                    "))
                    ->first();
                info('process closed document', [
                    "remark" => $doc->Remarks,
                    "Confirmation" => $doc->Confirmation
                ]);
                if (($doc->Remarks != 'SPPB' || $doc->Confirmation != 'Confirm' || !empty($doc->EmailToBcDate)) && date('Y', strtotime($doc->Date_bl)) >= 2024) {
                    throw new \App\Exceptions\CustomException("Cannot closed document! Document status must be SPPB and Confirmation is Confirm");
                } else {
                    MonitorDoc::where("DocEntry", $docEntry)
                        ->update([
                            'Flags' => 'J',
                            // "BC_type_key" => 0,
                            'status' => 'Closed',
                            'updated_at' => Carbon::now(),
                            'Updated_by' => $request->user()->username,
                        ]);

                    $this->createDocProcessLog(
                        $docEntry,
                        'Closed',
                        date('Y-m-d'),
                        Auth::user()->id,
                        Carbon::now()
                    );
                    DB::commit();
                }

                return response()->json('Document updated');
            } else {
                return response()->json([
                    'message' => 'Action not authorize'
                ], 400);
            }
        } catch (\Exception $th) {
            DB::rollBack();
            return response()->json([
                'message' => $th->getMessage()
            ], 400);
        }
    }

    /**
     * @param Request $request
     * @param $docEntry
     *
     * @return JsonResponse
     */
    public function reOpenDocStatus(Request $request, $docEntry)
    {
        try {
            $doc = DB::table('T_MDOC')->where('DocEntry', '=', $docEntry)->get();
            switch ($request->targetStatus) {
                case 'J':
                    $status = 'Open';
                    $flag = 'J';

                    $reopen = DB::table('T_MDOC')->where('DocEntry', '=', $docEntry)
                        ->update([
                            'Flags' => $flag,
                            // "BC_type_key" => 0,
                            'status' => $status,
                            'updated_at' => Carbon::now(),
                            'Updated_by' => $request->user()->username,
                        ]);

                    $this->createDocProcessLog(
                        $docEntry,
                        're-Open',
                        date('Y-m-d'),
                        Auth::user()->id,
                        Carbon::now()
                    );
                    break;
                case 'T':
                    $status = 'Process to Tenant';
                    $flag = 'T';
                    $reopen = DB::table('T_MDOC')->where('DocEntry', '=', $docEntry)
                        ->update([
                            'Flags' => $flag,
                            'status' => $status,
                            'updated_at' => Carbon::now(),
                            'Updated_by' => $request->user()->username,
                        ]);

                    $this->createDocProcessLog(
                        $docEntry,
                        'Process to Tenant',
                        date('Y-m-d'),
                        Auth::user()->id,
                        Carbon::now()
                    );

                    break;
                case 'J2':
                    $status = 'Process to Jakarta';
                    $flag = 'J';
                    $reopen = DB::table('T_MDOC')->where('DocEntry', '=', $docEntry)
                        ->update([
                            'Flags' => $flag,
                            'status' => $status,
                            'updated_at' => Carbon::now(),
                            'Updated_by' => $request->user()->username,
                        ]);

                    $this->createDocProcessLog(
                        $docEntry,
                        'Process to Jakarta',
                        date('Y-m-d'),
                        Auth::user()->id,
                        Carbon::now()
                    );

                    break;

                case 'PPJK':
                    $status = 'Process to PPJK';
                    $flag = 'PPJK';
                    $reopen = DB::table('T_MDOC')->where('DocEntry', '=', $docEntry)
                        ->update([
                            'Flags' => $flag,
                            'status' => $status,
                            'updated_at' => Carbon::now(),
                            'Updated_by' => $request->user()->username,
                        ]);

                    $this->createDocProcessLog(
                        $docEntry,
                        'Process to PPJK',
                        date('Y-m-d'),
                        Auth::user()->id,
                        Carbon::now()
                    );

                    break;

                default:
                    $status = 'Open';
                    $flag = 'J';

                    $reopen = DB::table('T_MDOC')->where('DocEntry', '=', $docEntry)
                        ->update([
                            'Flags' => $flag,
                            // "BC_type_key" => 0,
                            'status' => $status,
                            'updated_at' => Carbon::now(),
                            'Updated_by' => $request->user()->username,
                        ]);

                    break;
            }

            // $this->updateHeaderColor($docEntry);
            return response()->json([
                'errors' => false,
                'message' => 'Document status Open!',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'errors' => true,
                'message' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param Request $request
     * @param int $id
     *
     * @return JsonResponse
     *
     * @throws \Exception
     * @throws \Throwable
     */
    public function update(Request $request, $id = null): JsonResponse
    {
        // $header = $this->getHeaderDoc($id, $request);
        // return response()->json([
        //     'date1'=>date('Y-m-d H:i:s', strtotime($header->updated_at)),
        //     'date2'=>date('Y-m-d H:i:s', strtotime($request->form['updated_at']))
        // ], 422);

        // if ($header) {
        //     if (date('Y-m-d H:i:s', strtotime($header->updated_at)) != date('Y-m-d H:i:s', strtotime($request->form['updated_at']))) {
        //         return response()->json([
        //             'errors' => true,
        //             'message' => 'Document already updated by other user, Please refresh your browser!',
        //         ], 422);
        //     }
        // }

        DB::beginTransaction();
        try {
            if ($this->validation($request)) {
                return response()->json([
                    'errors' => true,
                    'validHeader' => true,
                    'message' => $this->validation($request),
                ], 422);
            }
            $details = collect($request->details);
            $form_data = $request->form;

            $count_voyage = $this->checkVoyage($id, $request->form['Voyage']);
            $doc_num = null;
            // get header
            $header = $this->getHeaderDoc($id, $request);
            // set created at
            $created = !empty($header) ? $header->created_at : Carbon::now();

            // if (empty($request->form['PostingDate'])) {
            //     return response()->json([
            //         "errors" => true,
            //         "message" => "Posting Date is required!"
            //     ]);
            // }
            // process header
            $doc_num = $this->processHeaderDoc($header, $created, $request);
            if ($doc_num) {
                $result = $this->loopDetails($details, $doc_num, $form_data, $id, $request);
                DB::commit();
                // ImportDocumentUpdated::dispatch($id, Auth::user()->id);
                return $result;
            }
            return response()->json([
                'errors' => true,
                'message' => 'Failed process header!',
            ], 422);
        } catch (\Exception $exception) {
            DB::rollBack();
            return response()->json([
                'errors' => true,
                'message' => $exception->getMessage(),
                'trace' => $exception->getTrace()
            ], 422);
        }
    }

    /**
     * Color
     * J = #ffd966 yellow- orange
     * T = #6fa8dc blue
     * P = #93c47d green
     * RJ = #e06666 red
     *
     * @param $request
     *
     * @return bool|\Illuminate\Support\MessageBag
     */

    public function validation($request)
    {
        $messages = [
            'form.Cargo.required' => 'Vessel Name is required!',
            'form.Vessel_arrive.required' => 'Vessel Arrive is required!',
            'form.Shipement.required' => 'Shipment no is required!',
            'form.Voyage.required' => 'Voyage no is required!',
            'form.Type.required' => 'Type no is required!',
        ];

        $validator = Validator::make($request->all(), [
            'form.Cargo' => 'required',
            'form.Vessel_arrive' => 'required',
            'form.Shipement' => 'required',
            'form.Voyage' => 'required',
            'form.Type' => 'required',
        ], $messages);

        $string_data = '';
        if ($validator->fails()) {
            foreach (collect($validator->messages()) as $error) {
                foreach ($error as $items) {
                    $string_data .= $items . " \n  ";
                }
            }
            return $string_data;
        }
        return false;
    }

    public function getCargo($name)
    {
        return DB::table('M_Cargo')->where('Name', '=', $name)->first();
    }

    /**
     * @param $header
     * @param $created
     * @param $request
     *
     * @return int|mixed
     */
    protected function processHeaderDoc($header, $created, $request)
    {
        $voyage = $request->form['Voyage'];

        $cargo = $this->getCargo($request->form['Cargo']);

        if ($header) {
            if (Auth::user()->hasRole('Admin Jakarta Master Can Update')) {
                $posting_date = $this->filterDate($header->PostingDate, $request->form['PostingDate']);

                $vessel_arrival_update = $request->form['Vessel_arrive'] ?
                    Carbon::parse($request->form['Vessel_arrive'])->addHours(8)->format('Y-m-d') : date('Y-m-d');

                $vessel_arrival_date = date('Y-m-d', strtotime($header->Vessel_arrive));
                $vessel_arrival_time = date('H:i:s', strtotime($header->Vessel_arrive));
                $vessel_arrival_req = date('Y-m-d', strtotime($request->form['Vessel_arrive']));
                $vessel_arrival_group = date(
                    'Y-m-d H:i:s',
                    strtotime($vessel_arrival_date . ' ' . $vessel_arrival_time)
                );
                $vessel_arrival_req_group = date(
                    'Y-m-d H:i:s',
                    strtotime($vessel_arrival_req . ' ' . $vessel_arrival_time)
                );
                $vessel_arrival = $vessel_arrival_date == $vessel_arrival_req ?
                    $vessel_arrival_group : $vessel_arrival_req_group;
                $header->Cargo = $request->form['Cargo'];
                $header->PortOfLoading = $request->form['PortOfLoading'];
                $header->Shipement_no = $request->form['Shipement_no'];
                $header->Vessel_arrive = $vessel_arrival_update;
                $header->Shipement = $request->form['Shipement'];
                $header->Remarks = $request->form['Remarks'];
                $header->Voyage = $request->form['Voyage'];
                $header->Type = $request->form['Type'];
                $header->PostingDate = $posting_date;
                // $header->DocNum = $this->generateDocNum($posting_date);
                $header->updated_at = Carbon::now();
                $header->created_at = $created;
                $header->Updated_by = Auth::user()->username;
                $header->GrtWeight = ($cargo) ? $cargo->GrossWeight : 0;
                $header->VesselFlag = ($cargo) ? $cargo->Flag : null;
                $header->Flags = $this->getFlags();
            } else {
                $header->updated_at = Carbon::now();
                $header->GrtWeight = ($cargo) ? $cargo->GrossWeight : 0;
                $header->VesselFlag = ($cargo) ? $cargo->Flag : null;
            }
            $header->save();
            // $this->updateHeaderColor($docNum);
        } else {
            $posting_date = $request->form['PostingDate'] ?
                Carbon::parse($request->form['PostingDate'])->addHours(8)->format('Y-m-d') : date('Y-m-d');

            $header = new TMDocHeader();
            $header->Cargo = $request->form['Cargo'];
            $header->PortOfLoading = $request->form['PortOfLoading'];
            $header->Shipement_no = $request->form['Shipement_no'];
            $header->Vessel_arrive = Carbon::parse($request->form['Vessel_arrive'])->addHours(8);
            $header->Shipement = $request->form['Shipement'];
            $header->Voyage = $request->form['Voyage'];
            $header->Type = $request->form['Type'];
            $header->Remarks = $request->form['Remarks'];
            $header->MatchKey = $request->form['MatchKey'];
            $header->PostingDate = $posting_date;
            $header->created_at = Carbon::now();
            $header->DocNum = $this->generateDocNum($posting_date);
            $header->Created_by = Auth::user()->username;
            $header->Flags = $this->getFlags();
            $header->GrtWeight = ($cargo) ? $cargo->GrossWeight : 0;
            $header->VesselFlag = ($cargo) ? $cargo->Flag : null;
            $header->status = 'Open';
            $header->GrossWeight = 0;
            $header->save();

            $this->updateHeaderColor($header->DocEntry);
        }
        // Log::info('cargo', [
        //     'cargo' => $cargo->Flag,
        //     'header' => $header,
        // ]);
        return $header->DocEntry;
    }

    /**
     * @param $date_header
     * @param $second_date
     *
     * @return string
     */
    public function filterDate($date_header, $second_date)
    {
        // dd((date('Y-m-d H:i:s', strtotime($date_header)) . ' == ' . date('Y-m-d H:i:s', strtotime($second_date))));

        // if (date('Y-m-d', strtotime($date_header)) == date('Y-m-d', strtotime($second_date))) {
        //     return $second_date;
        // }
        // return Carbon::parse($second_date)->addHours(8)->format('Y-m-d');
        // return Carbon::parse($second_date)->addDays(1)->format('Y-m-d');

        if ($second_date) {
            // Log::info('date diff', [
            //     'check' => (date('Y-m-d H:i:s', strtotime($date_header)) == date('Y-m-d H:i:s', strtotime($second_date))),
            //     'date_header' => date('Y-m-d H:i:s', strtotime($date_header)),
            //     'date_header1' => $date_header,
            //     'second_date' => date('Y-m-d H:i:s', strtotime($second_date)),
            //     'second_date1' => $second_date
            // ]);
            if (date('Y-m-d H:i:s', strtotime($date_header)) == date('Y-m-d H:i:s', strtotime($second_date))) {
                return Carbon::parse($second_date)->addHours(8);
                // return $second_date;
            } else {
                return Carbon::parse($second_date)->addHours(8);
            }
            // return Carbon::parse($second_date);
        } else {
            return null;
        }
    }

    /**
     * @return string
     */
    public function getFlags()
    {
        switch (Auth::user()->permission_id) {
            case '3':
                return 'PPJK';
            case '5':
                return 'T';
            case '6':
                return 'J';
            case '1':
                return 'SU';
            case '4':
                return 'BC';
            default:
                return 'DEFAULT';
        }
    }

    /**
     * @param Request $request
     *
     * @return JsonResponse
     */
    public function reSendEmailChange(Request $request)
    {
        try {
            $items = $request->details;
            $details = $this->transformSingleRow(collect($items));
            // throw new \App\Exceptions\CustomException(json_encode($details['No_bl']), 1);
            $form = $request->form;
            // dd($form);
            if ($request->type == 'SPPD') {
                // $this->processSendEmailSPPD($items, $form);
                $document = DB::table('T_MDOC')
                    ->where('DocEntry', '=', $details["DocEntry"])
                    ->selectRaw('SPPD_No, SPPD_date')
                    ->first();
                // dd($document);

                DB::table('T_MDOC')
                    ->where('DocEntry', '=', $details["DocEntry"])
                    ->update([
                        'SPPD_No_update' => $details["SPPD_No"],
                        'SPPD_Date_update' => Carbon::now(),
                    ]);
                if ($details["Type"] == '2.3') {
                    $this->sendEmailSPPD($details, $form, $document);
                }
                return response()->json([
                    'errors' => false,
                    'message' => 'SPPD Email Sent!',
                ]);
            }
            if ($request->type == 'SPPB') {
                // $this->processSendEmailSPPB($items, $form);
                $document = DB::table('T_MDOC')
                    ->where('DocEntry', '=', $details["DocEntry"])
                    ->selectRaw('SPPB_No, SPPB_date')
                    ->first();

                DB::table('T_MDOC')
                    ->where('DocEntry', '=', $details["DocEntry"])
                    ->update([
                        'SPPB_No_update' => $details["SPPB_No"],
                        'SPPB_Date_update' => Carbon::now(),
                    ]);
                if ($details["Type"] == '2.3') {
                    // return response()->json($details);
                    $this->sendEmailSPPB($details, $form, $document);
                }
                return response()->json([
                    'errors' => false,
                    'message' => 'SPPB Email Sent!',
                ]);
            }
        } catch (\Exception $e) {
            return response()->json([
                'errors' => true,
                'message' => $e->getMessage(),
                'trace' => $e->getTrace()
            ]);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     *
     * @return void
     */
    public function destroy(Request $request, $id)
    {
        DB::beginTransaction();
        try {
            // $doc = MonitorDoc::where("DocNum", $id)
            //     ->where("DocType", "Import")
            //     ->count();
            // if ($doc > 0) {
            //     throw new \App\Exceptions\CustomException("Cannot remove vessel because it has details. Please delete details first!");
            // }

            if (!$request->user()->hasAnyRole(['Superuser', 'Admin Jakarta Master'])) {
                throw new \App\Exceptions\CustomException("Unauthorize to delete this vessel!");
            }
            TMDocHeader::where("DocEntry", $id)
                ->update([
                    "Deleted" => 'Y',
                    "Updated_by" => $request->user()->username
                ]);

            MonitorDoc::where("DocNum", $id)
                ->where("DocType", "Import")
                ->update([
                    'Deleted' => 'Y',
                    "Updated_by" => $request->user()->username
                ]);

            DB::commit();
            return response()->json([
                "message" => "Vessel deleted!"
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                "message" => $e->getMessage()
            ], 500);
        }
    }

    /**
     * @param Request $request
     *
     * @return JsonResponse
     */
    public function maxDocImport(Request $request)
    {
        $max_num = DB::table('T_MDOC_Header')
            ->selectRaw('ISNULL(MAX(DocEntry), 1) as DocEntry')->first();
        return response()->json($max_num);
    }

    /**
     * @param $secretKey
     *
     * @return int
     */
    public function checkSecretKey($secretKey)
    {
        return DB::table('T_MDOC')
            ->where('SecretKey', '=', $secretKey)
            ->count();
    }

    /**
     * @param Request $request
     * @param $docEntry
     *
     * @return JsonResponse
     */
    public function deleteMedia(Request $request, $docEntry)
    {
        try {
            $attach = DB::table('M_Doc_attachment')->where('DocEntry', '=', $docEntry)->first();
            if ($attach->Created_by != Auth::user()->username) {
                return response()->json([
                    'error' => false,
                    'message' => 'Cannot delete file with this user!',
                ]);
            }
            $attachment = $request->name;
            $data_file = custom_disk_path('/docs/' . $attachment);
            custom_disk_delete($data_file);
            DB::table('M_Doc_attachment')->where('DocEntry', '=', $docEntry)->delete();

            return response()->json([
                'error' => false,
                'message' => 'File deleted successfully!',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => true,
                'message' => $e->getMessage(),
            ], 422);
        }
    }

    /**
     * @param Request $request
     *
     * @return JsonResponse
     */
    public function getRowData(Request $request)
    {
        $docNum = $request->docNum;
        $status = $request->status;

        $result = DB::table('T_MDOC AS a')
            ->selectRaw('
                b.Name
                ,a.Flags
                ,a.Status
            ')
            ->where('a.DocNum', '=', $docNum)
            ->leftJoin('M_Tenant as b', 'b.DocEntry', 'a.Tenant_key')
            ->get();
        return response()->json($result);
    }

    /**
     *
     */
    public function syncBpAndCargo()
    {
        $doc = DB::table('T_MDOC')->get();
        foreach ($doc as $key => $value) {
            $bp = DB::table('M_BP')->where('Name', '=', $value->BP)->first();
            $cargo = DB::table('M_Cargo')->where('Name', '=', $value->Cargo)->first();
            DB::table('T_MDOC')->where('DocEntry', '=', $value->DocEntry)
                ->update([
                    'BPNum' => $bp->DocEntry,
                    'CargoNum' => $cargo->DocEntry,
                ]);
        }
    }

    /**
     * @param Request $request
     *
     * @return JsonResponse
     */
    public function loadItem(Request $request)
    {
        // $driver = 'HDBODBC';
        // $servername = '**********:30015';
        // $db_name = 'SMI_LIVE';
        // $username = 'SYSTEM';
        // $password = 'hanaSmi*8Ultra';

        // $search = isset($request->q) ? (string) $request->q : '';
        // $conn = new PDO("odbc:Driver=${driver};ServerNode=${servername}; Uid=${username};Pwd=${password};CHAR_AS_UTF8=true;");

        // $stm = $conn->query("SELECT \"ItemCode\", \"ItemName\", \"FrgnName\" FROM \"SMI_LIVE\".\"OITM\"
        //     where \"ItemCode\" like '%${search}%' limit 20  ");
        $arr = [];
        $itmName = [];
        $frgnName = [];
        // while ($row = $stm->fetch(PDO::FETCH_ASSOC)) {
        //     $arr[] = $row['ItemCode'];
        //     $itmName[] = $row['ItemName'];
        //     // $frgnName[] = utf8_encode($row['FrgnName']);
        //     $frgnName[] = $row['FrgnName'];
        //     // $frgnName[] = mb_convert_encoding($row['FrgnName'], 'UTF-8');
        // }
        //return response()->json(['ItemCode'=>$itemcd,'FrgName'=>$FrgName]);
        return response()->json([
            'items' => $arr,
            'ItemName' => $itmName,
            'FrgnName' => $frgnName,
        ]);
    }

    /**
     * @param Request $request
     *
     * @return JsonResponse
     */
    public function removeDoc(Request $request)
    {
        $data_type = isset($request->type) ? (string) $request->type : '';
        $doc_entry = $request->DocEntry;
        $count_data = 0;
        switch ($data_type) {
            case 'details':
                $listId = [];
                foreach ($doc_entry as $data) {
                    $listId[] = $data['DocEntry'];
                }

                $check_billing = DB::table("T_MDOC AS A")
                    ->leftJoin("BEXP AS B", "A.DocEntry", "B.BaseId")
                    ->leftJoin("BHIMP AS C", "B.DocNum", "C.DocEntry")
                    ->where("C.Status", "<>", "Cancel")
                    ->whereIn('A.DocEntry', $listId)
                    ->count();
                if ($check_billing) {
                    return response()->json([
                        "message" => "Document already in billing!"
                    ], 422);
                }
                $count_data = DB::table('T_MDOC')->whereIn('DocEntry', $listId)->count();
                break;
            case 'inv':
                $count_data = DB::table('T_MDOC_bl')->whereIn('DocEntry', $doc_entry)->count();
                break;
            case 'inv-details':
                $count_data = DB::table('T_MDOC_inv')->whereIn('DocEntry', $doc_entry)->count();
                break;
            case 'inv-details-sub':
                $count_data = DB::table('T_MDOC_inv_sub')->whereIn('DocEntry', $doc_entry)->count();
                break;
        }

        if ($count_data > 0) {
            switch ($data_type) {
                case 'details':
                    $id_user = $request->user()->id;
                    $check_user = MonitorDoc::whereIn('DocEntry', $listId)
                        ->where("Created_id", "=", $id_user)
                        ->count();
                    if ($check_user > 0) {
                        DB::table('T_MDOC')->whereIn('DocEntry', $listId)->update([
                            'Deleted' => 'Y',
                            'updated_at' => Carbon::now(),
                            'Updated_by' => Auth::user()->username,
                            'Updated_id' => Auth::user()->id,
                            'DeletedAt' => Carbon::now(),
                            'DeleteBy' => Auth::user()->username,
                        ]);
                    } else {
                        return response()->json([
                            "message" => "You are not authorize to delete this documents!"
                        ], 422);
                    }
                    break;
                case 'inv':
                    $username = $request->user()->username;
                    $check_user = MonitorDocBl::whereIn('DocEntry', $doc_entry)
                        ->where("Created_by", "=", $username)
                        ->count();
                    if ($check_user > 0) {
                        DB::table('T_MDOC_bl')->whereIn('DocEntry', $doc_entry)->update([
                            'Deleted' => 'Y',
                            'updated_at' => Carbon::now(),
                            'Updated_by' => Auth::user()->username,
                            'DeletedAt' => Carbon::now(),
                            'DeleteBy' => Auth::user()->username,
                        ]);
                    } else {
                        return response()->json([
                            "message" => "You are not authorize to delete this documents!"
                        ], 422);
                    }
                    break;
                case 'inv-details':
                    $username = $request->user()->username;
                    $check_user = MonitorDocInv::whereIn('DocEntry', $doc_entry)
                        ->where("Created_by", "=", $username)
                        ->count();
                    if ($check_user > 0) {
                        DB::table('T_MDOC_inv')->whereIn('DocEntry', $doc_entry)->update([
                            'Deleted' => 'Y',
                            'updated_at' => Carbon::now(),
                            'Updated_by' => Auth::user()->username,
                            'DeletedAt' => Carbon::now(),
                            'DeleteBy' => Auth::user()->username,
                        ]);
                    } else {
                        return response()->json([
                            "message" => "You are not authorize to delete this documents!"
                        ], 422);
                    }
                    break;
                case 'inv-details-sub':
                    DB::table('T_MDOC_inv_sub')->whereIn('DocEntry', $doc_entry)->update([
                        'Deleted' => 'Y',
                        'updated_at' => Carbon::now(),
                        'Updated_by' => Auth::user()->username,
                        'DeletedAt' => Carbon::now(),
                        'DeleteBy' => Auth::user()->username,
                    ]);
                    break;
            }
            return response()->json([
                'message' => "${count_data} rows removed, please update the document!",
            ]);
        }
        return response()->json([
            'message' => 'Document not found!',
        ]);
    }

    /**
     * @param $request
     *
     * @return string|null
     */
    public function toDate($request)
    {
        if (!empty($request)) {
            $date = Carbon::parse($request);
            return $date->format('Y-m-d');
        }
        return null;
    }

    public function restoreHeader(Request $request, $docEntry)
    {
        try {
            DB::table('T_MDOC_Header')
                ->where('DocEntry', '=', $docEntry)
                ->update([
                    'Deleted' => 'N',
                    'DocStatus' => 'Open'
                ]);

            DB::table("T_MDOC")
                ->where("DocNum", "=", $docEntry)
                ->where("DocType", "=", "Import")
                ->update([
                    'Deleted' => 'N',
                ]);
            return response()->json([
                'success' => true,
            ]);
        } catch (\Exception $e) {
            return response()->json($e->getMessage());
        }
    }

    /**
     * @param Request $request
     * @param $docEntry
     *
     * @return JsonResponse
     */
    public function deleteHeader(Request $request, $docEntry)
    {
        try {
            $remark = $request->remark ? $request->remark : null;
            DB::table('T_MDOC_Header')
                ->where('DocEntry', '=', $docEntry)
                ->update([
                    'Remarks' => $remark,
                    'Deleted' => 'Y',
                    'DocStatus' => 'Deleted'
                ]);

            DB::table("T_MDOC")
                ->where("DocNum", "=", $docEntry)
                ->where("DocType", "=", "Import")
                ->update([
                    'Deleted' => 'Y',
                ]);
            return response()->json([
                'success' => true,
            ]);
        } catch (\Exception $e) {
            return response()->json($e->getMessage());
        }
    }

    public function getRemarkHeader($docEntry)
    {
        $remark = DB::table('T_MDOC_Header')->where('DocEntry', '=', $docEntry)
            ->select('Remarks')
            ->first();
        return response()->json($remark);
    }

    /**
     * Show the form for creating a new resource.
     *ap
     *
     * @return Response
     */
    public function create()
    {
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param Request $request
     *
     * @return JsonResponse|Response
     */
    public function store(Request $request)
    {
        try {
            $form_data = $request->form;
            $doc_entry = $request->docEntry;
            $doc_num = null;
            // get header
            $header = $this->getHeaderDoc($doc_entry, $request);
            // set created at
            $created = !empty($header) ? $header->created_at : Carbon::now();
            // process header
            // $this->processHeaderDoc($header, $created, $request);
            $vessel_arrival_date = date('Y-m-d', strtotime($header->Vessel_arrive));
            $request_arrival_date = date('H:i:s', strtotime($request->form['Vessel_arrive']));
            $group_vessel_arrival = Carbon::parse(
                date('Y-m-d H:i:s', strtotime($vessel_arrival_date . ' ' . $request_arrival_date))
            )->addDays(-1)->format('Y-m-d H:i:s');
            $vessel_arrival_format = date('Y-m-d H:i:s', strtotime($header->Vessel_arrive));
            $vessel_arrival = $vessel_arrival_format == $group_vessel_arrival ?
                $vessel_arrival_format : $group_vessel_arrival;

            if (!isset($request->form['Reportdate'])) {
                return response()->json([
                    'errors' => true,
                    'message' => 'Report Date cannot be null!',
                ], 422);
            }

            $berthing_date = $header->BerthingDate == $request->form['BerthingDate'] ?
                $request->form['BerthingDate'] : Carbon::parse($request->form['BerthingDate'])->addHours(8);
            $anchorage_date = $header->AnchorageDate == $request->form['AnchorageDate'] ?
                $request->form['AnchorageDate'] : Carbon::parse($request->form['AnchorageDate'])->addHours(8);
            $vessel_departure = $header->VesselDeparture == $request->form['VesselDeparture'] ?
                $request->form['VesselDeparture'] : Carbon::parse($request->form['VesselDeparture'])->addHours(8);
            $unloading = $header->UnloadingDate == $request->form['UnloadingDate'] ?
                $request->form['UnloadingDate'] : Carbon::parse($request->form['UnloadingDate'])->addHours(8);
            $finish_unloading = $header->FinishUnloadingDate == $request->form['FinishUnloadingDate'] ?
                $request->form['FinishUnloadingDate'] : Carbon::parse($request->form['FinishUnloadingDate'])->addHours(8);
            $report_date = $header->Reportdate == $request->form['Reportdate'] ?
                $request->form['Reportdate'] : Carbon::parse($request->form['Reportdate'])->addHours(8);

            $header->Reportdate = $report_date;
            $header->VesselDeparture = $vessel_departure;
            $header->Vessel_arrive = $vessel_arrival;
            $header->BerthingDate = $berthing_date;
            $header->UnloadingDate = $unloading;
            $header->FinishUnloadingDate = $finish_unloading;
            $header->AnchorageDate = $anchorage_date;
            $header->Jetty = $this->getJettyByName($request->form['Jetty']);
            $header->VesselStatus = $request->form['VesselStatus'];
            $header->DestinationPort = $request->form['DestinationPort'];
            $header->GrossWeight = ($request->form['GrossWeight']) ? $request->form['GrossWeight'] : 0;
            $header->VesselFlag = $request->form['VesselFlag'];
            $header->updated_at = Carbon::now();
            $header->JettyUpdate = Carbon::now();
            $header->created_at = $created;
            $header->Updated_by = Auth::user()->username;
            $header->save();

            $details = collect($request->details);

            if ($header) {
                if ($details) {
                    foreach ($details as $items) {
                        $monitor_doc = MonitorDoc::where('DocEntry', '=', $items['DocEntry'])->first();
                        if ($monitor_doc) {
                            $monitor_doc->Agent = $this->getAgent($items['Agent']) ?
                                $this->getAgent($items['Agent']) : null;
                            $monitor_doc->save();

                            $billing = DB::table('BEXP')
                                ->where('BaseId', '=', $monitor_doc->DocEntry)
                                ->count();
                            if ($billing > 0) {
                                $agent = array_key_exists('Agent', $items) ? $this->getAgent($items['Agent']) : null;
                                DB::table('BEXP')
                                    ->where('BaseId', '=', $monitor_doc->DocEntry)
                                    ->update([
                                        'Agent' => $agent
                                    ]);
                            }
                        }
                    }
                }
            }

            return response()->json([
                'errors' => false,
                'message' => 'Data updated',
            ]);
        } catch (\Exception $exception) {
            return response()->json([
                'errors' => true,
                'message' => $exception->getMessage(),
                'line' => $exception->getLine(),
                'trace' => $exception->getTrace(),
            ], 422);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param int $id
     *
     * @return JsonResponse
     */
    public function show($docEntry)
    {
        $data = DB::table('T_MDOC')->where('DocEntry', '=', $docEntry)->first();
        return response()->json($data);
    }

    /**
     * @param Request $request
     *
     * @return JsonResponse
     */
    public function getESign(Request $request)
    {
        $tenant = $this->getTenant($request->details[4]);
        $status_request = DB::table('L_esign')
            ->where('id_request', '=', 'Y')
            ->where('no_bl', '=', strtoupper(Str::slug($request->details[0])))
            ->where('tenant', '=', $tenant->DocEntry)
            // ->where('bc_type', '=', '20')
            ->count();

        $last_request = DB::table('L_esign')
            ->where('id_request', '=', 'Y')
            ->where('no_bl', '=', strtoupper(Str::slug($request->details[0])))
            ->where('tenant', '=', $tenant->DocEntry)
            ->orderBy('DocEntry', 'DESC')
            // ->where('bc_type', '=', '20')
            ->first();

        $status_download = DB::table('L_esign')
            ->where('id_download', '=', 'Y')
            ->where('no_bl', '=', strtoupper(Str::slug($request->details[0])))
            ->where('tenant', '=', $tenant->DocEntry)
            // ->where('bc_type', '=', '20')
            ->count();

        $last_download = DB::table('L_esign')
            ->where('id_download', '=', 'Y')
            ->where('no_bl', '=', strtoupper(Str::slug($request->details[0])))
            ->where('tenant', '=', $tenant->DocEntry)
            ->orderBy('DocEntry', 'DESC')
            // ->where('bc_type', '=', '20')
            ->first();

        $row_data = [
            [
                'action' => 'Request e-Sign',
                'type' => 'request',
                'status' => $status_request > 0 ? 'Req ' . $status_request . '- BC ' . $last_request->bc_type : 'Open',
            ],
            [
                'action' => 'Download e-Sign',
                'type' => 'download',
                'status' => '',
                // 'status' => ($status_download > 0) ? 'Download '.($status_download) . '- BC '. $last_download->bc_type : 'Open'
            ],
        ];

        return response()->json([
            'rows' => $row_data,
        ]);
    }

    /**
     * @param  $parameters
     *
     * @return
     */
    public function getTenant($parameters)
    {
        return DB::table('M_Tenant')->where('Name', '=', $parameters)->first();
    }

    /**
     * @param Request $request
     *
     * @return JsonResponse
     */
    public function importType(Request $request)
    {
        return response()->json([
            'rows' => [
                [
                    'value' => '',
                    'title' => 'None',
                ],
                [
                    'value' => 'E',
                    'title' => 'Equipment',
                ],
                [
                    'value' => 'R',
                    'title' => 'Raw Materials',
                ],
            ],
        ]);
    }
}
