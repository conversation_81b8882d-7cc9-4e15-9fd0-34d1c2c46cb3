<?php

namespace App\Exports;

use App\Models\View\ViewCherryCalendar;
use App\Models\View\ViewMasterShift;
use App\Models\WorkShift\EmployeeWorkShift;
use Carbon\Carbon;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;

class WorkShiftExportAttendance implements FromView, ShouldAutoSize
{
    use Exportable;

    protected $departments;
    protected $date_from;
    protected $date_to;
    protected $period;
    protected $request;

    public function __construct($departments, $date_from, $date_to, $period, $request)
    {
        $this->departments = $departments;
        $this->date_from = $date_from;
        $this->date_to = $date_to;
        $this->period = $period;
        $this->request = $request;
    }

    /**
     * @return View
     */
    public function view(): View
    {
        $check_superuser = $this->request->user()->hasRole('Superuser');
        $user_id = $this->request->user()->id;

        $ws_details = DB::table('WorkShiftDetails AS A')
            ->select("A.*", DB::raw("(SELECT top 1 order_line FROM Employees where nik = A.nik) AS order_line"))
            // ->leftJoin("Employees as B", "A.nik", "B.nik")
            ->whereBetween('A.shift_date', [$this->date_from, $this->date_to])
            ->where('A.department', 'LIKE', '%%')
            ->when($check_superuser, function ($query) use (
                $check_superuser,
                $user_id
            ) {
                if (!$check_superuser) {
                    $data_query = $query->where('A.created_by', '=', $user_id);
                } else {
                    $data_query = $query;
                }

                return $data_query;
            })
            // ->where('A.created_by', '=', auth()->user()->id)
            ->whereNotIn('position', ['Shipping Manager', 'Shipping Superintendent', 'Shipping Supervisor', 'Pandu Kapal'])
            ->whereNotNull("A.overtime")
            ->whereNotIn("A.overtime", ['R', '-'])
            ->whereRaw("(select status from WorkShifts where id = A.work_shift_id) <> 'cancel'")
            ->orderBy('A.nik')
            ->orderBy('A.shift_date')
            ->get();

        // $array = json_decode(file_get_contents("https://raw.githubusercontent.com/guangrei/Json-Indonesia-holidays/master/calendar.json"), true);
        $array = json_decode(file_get_contents(public_path("calendar.json")), true);
        $check_calendar = ViewCherryCalendar::where('Company', '=', 'PT BDT')
            ->where('Calendar', '=', 'Morowali')
            ->get();

        $list_shift2 = ViewMasterShift::whereIn('Name', ['BDT 2'])
                    ->first();

        $list_shift1 = ViewMasterShift::whereIn('Name', ['BDT 1'])
                    ->first();
        $list_shift_off = ViewMasterShift::whereIn('Name', ['OFF'])
                    ->whereNotNull('TimeStart')
                    ->orderBY('TimeStart', 'desc')
                    ->first();

        $rows = [];
        $array_code = ['CAM','CBA','CFV','CFV+CT','CG','CH','CIM','CKA','CKM','CL','CM','CT','CTB','Cuti Bersama','D','I','IC','SD','SK', 'R', '-'];
        foreach ($ws_details as $ws_detail) {
            // check if not have overtime
            $overtime = null;
            if (Str::contains($ws_detail->overtime, ['CFV'])) {
                $overtime = Str::after($ws_detail->overtime, 'CFV');
            } elseif (Str::contains($ws_detail->overtime, $array_code) || empty($ws_detail->overtime)) {
                $overtime = 0;
            } elseif ($ws_detail->overtime == 'H' || $ws_detail->overtime == 'HM' || $ws_detail->overtime == 'M') {
                $overtime = 0;
            } elseif (is_int($ws_detail->overtime)) {
                $overtime = $ws_detail->overtime;
            } elseif (Str::contains($ws_detail->overtime, ['SM'])) {
                $overtime = Str::before($ws_detail->overtime, 'SM');
            } elseif (Str::contains($ws_detail->overtime, ['HL'])) {
                $overtime = Str::before($ws_detail->overtime, 'HL');
            } elseif (Str::contains($ws_detail->overtime, ['HLM'])) {
                $overtime = Str::before($ws_detail->overtime, 'HLM');
            } else {
                $overtime = $ws_detail->overtime;
            }
            $date = Carbon::parse($ws_detail->shift_date)->locale('id');
            $date->settings(['formatFunction' => 'translatedFormat']);

            // echo $date->format('l, j F Y ; h:i a'); // Selasa, 16 Maret 2021 ; 08:27 pagi
            $temp_array = [
                'company' => 'PT BDT',
                'nik' => $ws_detail->nik,
                'name' => $ws_detail->name,
                'shift_date' => $date->format('d/m/Y'),
                'shift_day' => $date->format('l'),
                'overtime' => $overtime,
                'overtime_code' => $ws_detail->overtime,
            ];
            $temp_array_2 = [];
            if (Str::contains($ws_detail->overtime, ['O'])) {
                $temp_array_2['shift_time_start'] = $list_shift_off->TimeStart;
                $temp_array_2['shift_time_end'] = $list_shift_off->TimeEnd;
                $temp_array_2['shift_name'] = 'OFF';
                $temp_array_2['time_start'] = null;
                $temp_array_2['time_end'] = null;
            } elseif (Str::contains($ws_detail->overtime, ['SM', 'HLM', 'HM'])) {
                $temp_array_2['shift_time_start'] = $list_shift2->TimeStart;
                $temp_array_2['shift_time_end'] = $list_shift2->TimeEnd;
                if (Str::contains($ws_detail->overtime, ['HLM'])) {
                    $temp_array_2['shift_name'] = 'OFF 2';
                } else {
                    $temp_array_2['shift_name'] = $list_shift2->Name;
                }
                $temp_array_2['time_start'] = $list_shift2->TimeStart;
                // $temp_array_2['time_end'] = $list_shift2->TimeStart;

                $temp_overtime = (int)$ws_detail->overtime;
                $temp_array_2['time_end'] = $this->checkEndTimeEndShift($ws_detail, $list_shift2, $temp_overtime, $check_calendar);
            } elseif (Str::contains($ws_detail->overtime, $array_code)) {
                $temp_array_2['shift_time_start'] = $list_shift1->TimeStart;
                $temp_array_2['shift_time_end'] = $list_shift1->TimeEnd;
                $temp_array_2['shift_name'] = 'BDT 1';
                $temp_array_2['time_start'] = null;
                $temp_array_2['time_end'] = null;
            } elseif ($ws_detail->overtime == 'M') {
                $temp_array_2['shift_time_start'] = $list_shift1->TimeStart;
                $temp_array_2['shift_time_end'] = $list_shift1->TimeEnd;
                $temp_array_2['shift_name'] = 'BDT 1';
                $temp_array_2['time_start'] = null;
                $temp_array_2['time_end'] = null;
            } elseif (Str::contains($ws_detail->overtime, ['H']) || (int)$ws_detail->overtime != 0) {
                $temp_array_2['shift_time_start'] = $list_shift1->TimeStart;
                $temp_array_2['shift_time_end'] = $list_shift1->TimeEnd;
                $temp_array_2['shift_name'] = $list_shift1->Name;
                $temp_array_2['time_start'] = $list_shift1->TimeStart;

                $temp_overtime = ($ws_detail->overtime == 'H') ? 7 : (int)$ws_detail->overtime;
                $temp_array_2['time_end'] = $this->checkEndTimeEndShift($ws_detail, $list_shift1, $temp_overtime, $check_calendar);
            } else {
                $temp_array_2['shift_time_start'] = null;
                $temp_array_2['shift_time_end'] = null;
                $temp_array_2['shift_name'] = null;
                $temp_array_2['time_start'] = null;
                $temp_array_2['time_end'] = null;
            }
            $rows[] = array_merge($temp_array, $temp_array_2);
        }

        // $collection = collect($rows);
        // $sorted = $collection->sortBy([
        //     ['shift_date', 'asc'],
        //     ['nik', 'asc'],
        // ]);

        // $rows = $sorted->values()->all();

        return view('export.excel.workshift', [
            'work_shifts' => $rows,
        ]);
    }

    protected function checkEndTimeEndShift($ws_detail, $list_shift, $temp_overtime, $array)
    {
        $date_now = $ws_detail->shift_date;
        $check_employee_reguler = EmployeeWorkShift::where('nik', '=', $ws_detail->nik)
                ->whereRaw("'${date_now}' >= date_from AND '${date_now}' <= date_to")
                ->orderBy('id', 'desc')
                ->first();

        foreach ($array as $value) {
            if (date('Y-m-d', strtotime($value->Startdate)) == $ws_detail->shift_date) {
                $tem_date_now = Carbon::parse($ws_detail->shift_date . ' '. $list_shift->TimeStart);
                return  $tem_date_now->addHours($temp_overtime)->format('H') . ':00';
            }
        }

        if ($check_employee_reguler || Str::contains($ws_detail->position, ['ADMINISTRASI', 'ADMIN'])) {
            if (date('D', strtotime($ws_detail->shift_date)) == 'Sun') {
                $tem_date_now = Carbon::parse($ws_detail->shift_date . ' '. $list_shift->TimeStart);
                return  $tem_date_now->addHours($temp_overtime)->format('H') . ':00';
            } elseif (date('D', strtotime($ws_detail->shift_date)) == 'Fri') {
                $tem_date_now = Carbon::parse($ws_detail->shift_date . ' '. $list_shift->TimeEnd);
                return  $tem_date_now->addHours(($temp_overtime-3))->format('H') . ':00';
            } else {
                $tem_date_now = Carbon::parse($ws_detail->shift_date . ' '. $list_shift->TimeEnd);
                return  $tem_date_now->addHours(($temp_overtime-1))->format('H') . ':00';
            }
        } else {
            if (date('D', strtotime($ws_detail->shift_date)) == 'Fri') {
                $tem_date_now = Carbon::parse($ws_detail->shift_date . ' '. $list_shift->TimeEnd);
                return  $tem_date_now->addHours(($temp_overtime-3))->format('H') . ':00';
            } else {
                $tem_date_now = Carbon::parse($ws_detail->shift_date . ' '. $list_shift->TimeEnd);
                return  $tem_date_now->addHours(($temp_overtime-1))->format('H') . ':00';
            }
        }
    }
}
