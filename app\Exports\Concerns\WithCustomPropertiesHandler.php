<?php

namespace App\Exports\Concerns;

use Maatwebsite\Excel\Sheet;
use Maatwebsite\Excel\Writer;

class WithCustomPropertiesHandler
{
    /**
     * @param WithCustomProperties $exportable
     * @param Writer $writer
     */
    public function __invoke(WithCustomProperties $exportable, Writer $writer, Sheet $sheet)
    {
        $writer
            ->getDelegate()
            ->getProperties()
            ->setDescription(
                $exportable->description()
            );
    }
}
