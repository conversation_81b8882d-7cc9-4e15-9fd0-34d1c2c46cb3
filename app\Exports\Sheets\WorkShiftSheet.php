<?php


namespace App\Exports\Sheets;

use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Events\BeforeSheet;
use Maatwebsite\Excel\Sheet;

class WorkShiftSheet implements FromView, ShouldAutoSize, WithEvents, WithTitle
{
    protected $work_shifts;
    protected $department;
    protected $period;
    protected $period_range;
    protected $header;

    public function __construct($work_shifts, $department, $period, $period_range, $header)
    {
        $this->work_shifts = $work_shifts;
        $this->department = $department;
        $this->period = $period;
        $this->period_range = $period_range;
        $this->header = $header;
    }

    /**
     * @return View
     */
    public function view(): View
    {
        return view(
            'export.excel.workshift_attendance',
            [
                'work_shifts' => $this->work_shifts,
                'period' => $this->period,
                'period_range' => $this->period_range,
                'department' => $this->department,
                'header' => $this->header,
            ]
        );
    }

    /**
     * @return array|\Closure[]
     */
    public function registerEvents(): array
    {
        return [
            BeforeSheet::class => function (BeforeSheet $event) {
                Sheet::macro('styleCells', function (Sheet $sheet, string $cellRange, array $style) {
                    $sheet->getDelegate()->getStyle($cellRange)->applyFromArray($style);
                });
            }
        ];
    }

    /**
     * @return string
     */
    public function title(): string
    {
        return $this->department;
    }
}
