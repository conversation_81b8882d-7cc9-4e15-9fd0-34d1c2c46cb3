<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('BEXP', function (Blueprint $table) {
            $table->index('DocNum');
            $table->index('TenantId');
            $table->index('Status');
            $table->index('Deleted');
            $table->index('BP');
            $table->index('Jetty');
            $table->index('Type');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('BEXP', function (Blueprint $table) {
            //
        });
    }
};
