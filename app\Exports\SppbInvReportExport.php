<?php

namespace App\Exports;

use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Events\AfterSheet;
use Maatwebsite\Excel\Events\BeforeSheet;
use Maatwebsite\Excel\Sheet;

class SppbInvReportExport implements FromView, ShouldAutoSize, WithEvents
{
    public $query;
    public $type;
    public function __construct($query, $type)
    {
        $this->query = $query;
        $this->type = $type;
    }
    /**
     * @return array
     */
    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event) {
                $cellRange = 'A1:W1'; // All headers
                $event->sheet->getDelegate()->getStyle($cellRange)->getFont()->setSize(12);
            },
            BeforeSheet::class => function (BeforeSheet $event) {
                Sheet::macro('styleCells', function (Sheet $sheet, string $cellRange, array $style) {
                    $sheet->getDelegate()->getStyle($cellRange)->applyFromArray($style);
                });
            }
        ];
    }

    /**
     * @param $bc_key
     * @return mixed
     */
    public function bcType($bc_key)
    {
        $bctype = DB::table("M_TBC")->where("DocEntry", "=", $bc_key)->first();
        return $bctype->Type;
    }

    /**
     * @return View
     */
    public function view(): View
    {

        $tenant = session('tenant');
        $type = session('type');

        $firstDateType = session('firstDateType');
        $dateType = session('dateType');
        $secondDateType = session('secondDateType');
        $firstNumType = session('firstNumType');
        $username = session('username');
        $secondNumType = session('secondNumType');

        if ($this->type != '2.0' && $this->type != '2.3') {
            return view(
                'export.report.sppb_inv_local',
                [
                    'doc' => $this->query,
                    'bc_desc' => $type,
                ]
            );
        } else {
            return view(
                'export.report.sppb_inv_import',
                [
                    'doc' => $this->query,
                    'bc_desc' => $type,
                ]
            );
        }
    }
}
