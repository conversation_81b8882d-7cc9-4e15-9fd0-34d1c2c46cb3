<?php

namespace App\Exports;

use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Events\AfterSheet;
use Maatwebsite\Excel\Events\BeforeSheet;
use Maatwebsite\Excel\Sheet;

class SpjmExport implements FromView, ShouldAutoSize, WithEvents
{
    public $query;
    public function __construct($query)
    {
        $this->query = $query;
    }
    /**
     * @return array
     */
    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event) {
                $cellRange = 'A1:W1'; // All headers
                $event->sheet->getDelegate()->getStyle($cellRange)->getFont()->setSize(12);
            },
            BeforeSheet::class => function (BeforeSheet $event) {
                Sheet::macro('styleCells', function (Sheet $sheet, string $cellRange, array $style) {
                    $sheet->getDelegate()->getStyle($cellRange)->applyFromArray($style);
                });
            }
        ];
    }

    /**
     * @param $bc_key
     * @return mixed
     */
    public function bcType($bc_key)
    {
        $bctype = DB::table("M_TBC")->where("DocEntry", "=", $bc_key)->first();
        return $bctype->Type;
    }

    /**
     * @return View
     */
    public function view(): View
    {
        return view(
            'export.report.spjm',
            [
                'doc' => $this->query,
            ]
        );
    }
}
