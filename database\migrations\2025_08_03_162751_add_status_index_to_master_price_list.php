<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('MPLPS', function (Blueprint $table) {
            $table->index("TenantKey");
            $table->index("Type");
            $table->index("PeriodStart");
            $table->index("PeriodEnd");
            $table->index("PSKey");
            $table->index("Currency");
            $table->index(["TenantKey", "Type", "PeriodStart", "PeriodEnd"]);
        });
        Schema::table('MPLSL', function (Blueprint $table) {
            $table->index("TenantKey");
            $table->index("Type");
            $table->index("PeriodStart");
            $table->index("PeriodEnd");
            $table->index("Currency");
            $table->index(["TenantKey", "Type", "PeriodStart", "PeriodEnd"]);
        });
        Schema::table('MPS', function (Blueprint $table) {
            $table->index("Active");
            $table->index("ItemCode");
        });
        Schema::table('BEXP', function (Blueprint $table) {
            $table->index("BaseId");
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('master_price_list', function (Blueprint $table) {
            //
        });
    }
};
