<?php

namespace App\Exceptions;

use App\Models\ApiLog;
use App\Traits\ApiResponse;
use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Illuminate\Support\Facades\Auth;
use Laravel\Passport\Exceptions\OAuthServerException;
use Throwable;

class Handler extends ExceptionHandler
{
    use ApiResponse;

    /**
     * The list of the inputs that are never flashed to the session on validation exceptions.
     *
     * @var array<int, string>
     */
    protected $dontFlash = [
        'current_password',
        'password',
        'password_confirmation',
    ];

    /**
     * Register the exception handling callbacks for the application.
     */
    public function register(): void
    {
        $this->reportable(function (Throwable $e) {
            //
        });
    }

    public function render($request, Throwable $exception)
    {
        if ($exception instanceof \Illuminate\Validation\ValidationException) {
            return parent::render($request, $exception);
        }

        if ($request->expectsJson()) {
            $responseData = [
                'status' => 'error',
                'message' => $exception->getMessage(),
                // 'meta' => [
                //     'timestamp' => now()->toDateTimeLocalString(),
                //     'execution_time' => microtime(true) - LARAVEL_START,
                //     'environment' => config('app.env'),
                // ]
            ];

            // Create API log entry
            ApiLog::create([
                'method' => $request->method(),
                'path' => $request->path(),
                'full_url' => $request->fullUrl(),
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'user_id' => Auth::id(),
                'request_headers' => json_encode($request->headers->all()),
                'request_body' => json_encode($request->all()),
                'response_status' => 500,
                'response_headers' => json_encode([]),
                'response_body' => json_encode($responseData),
                'execution_time' => microtime(true) - LARAVEL_START,
                'environment' => config('app.env'),
                'client_id' => $request->token_data->client_id ?? null,
                'source_ip' => $request->header('X-Forwarded-For', $request->ip()),
                'request_timestamp' => now(),
                'response_timestamp' => now(),
                'request_size' => strlen(json_encode($request->all())),
                'response_size' => strlen(json_encode($responseData))
            ]);

            // return response()->json($responseData, 500);
            $response = [];
            if ($exception && config('app.debug')) {
                $response = [
                    "data" => $exception->getTrace()
                ];
            }

            $code = $exception->getCode();
            if ($exception instanceof OAuthServerException) {
                $code = $this->getPassportHttpCode($exception->getCode());
            }

            // info("code " . $exception->getCode(), [
            //     'class' => get_class($exception),
            // ]);

            return $this->responseApi(
                $response,
                $exception->getMessage(),
                $code,
                "error"
            );
        }

        return parent::render($request, $exception);
    }
}
