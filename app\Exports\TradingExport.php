<?php
namespace App\Exports;

use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;

class TradingExport implements FromCollection, WithHeadings, ShouldAutoSize
{
    private $docEntry;
    /**
     * @return \Illuminate\Support\Collection
     */
    public function __construct($docEntry)
    {
        $this->docEntry = $docEntry;
    }
    public function collection()
    {
        $docEntrynya = $this->docEntry;
        $InvoiceDetail = DB::table('T_MDOC_inv_sub')->select('HsCode', 'ItemCode', 'ItemName', 'UoM', 'Qty', 'LineTotal', 'UnitPrice', 'ActQty', 'ActTotal', 'ActUnitPrice')->where('DocNum', '=', $docEntrynya)->where('Type', '=', 'T')->get();
        return $InvoiceDetail;
    }
    public function headings(): array
    {
        $cars = array
            ("HsCode", "Item Code", "Item Name", "UoM", "Quantity", "Line Total", 'Unit Price', 'Actual Quantity', 'Actual Line Total', 'Actual Unit Price',
        );
        return $cars;
    }
    // public function headingRow(): int
    // {
    //     return 2;
    // }
}
