<?php

namespace App\Http\Controllers\Billing;

use App\Http\Controllers\Controller;
use App\Jobs\BillingJobs;
use App\Models\Attachment\DocAttachment;
use App\Models\Billing\BEXP;
use App\Models\Billing\BEXP1;
use App\Models\Billing\HBIMP;
use App\Models\Port\Signature;
use App\Traits\BillingHelper;
use App\Traits\ImportHelper;
use App\Traits\PostingPeriodHelper;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class BillingImportController extends Controller
{
    use PostingPeriodHelper;
    use ImportHelper, BillingHelper;

    public function __construct()
    {
        $this->middleware(['direct_permission:Import-index'])->only(['index', 'dataBilling', 'getExportData', 'dataCargo']);
        $this->middleware(['direct_permission:Import-store'])->only(['store', 'storeExportDetails']);
        $this->middleware(['direct_permission:Import-edits'])->only(['update', 'cancelDoc']);
        $this->middleware(['direct_permission:Import-erase'])->only('destroy');
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $pagination = (object) $request->pagination;
        $year_local = date('Y');
        $pages = isset($pagination->page) ? (int) $pagination->page : 1;
        $filter = isset($request->filter) ? (string) $request->filter : $year_local;
        $row_data = isset($request->itemsPerPage) ? (int) $request->itemsPerPage : 20;
        // $sorts = isset($request->sort) ? (string)$request->sort : "Shipment";
        $sorts = isset($request->sortDesc[0]) ? $request->sortBy[0]['key'] : 'DocNum';
        $order = isset($request->sortDesc[0]) ? $request->sortBy[0]['order'] : 'desc';
        $order = isset($request->order) ? (string) $request->order : 'desc';
        $data_status = isset($request->dataStatus) ? (string) $request->dataStatus : 'Open';

        $search = isset($request->q) ? (string) $request->q : '';
        $select_data = isset($request->selectData) ? (string) $request->selectData : 'DocNum';
        $offset = $request->page;
        $row_data = $request->itemsPerPage;
        $username = $request->user()->username;
        $permission_id = $request->user()->permission_id;

        $result = [];
        $query = DB::table('BHIMP AS T2')
            ->selectRaw("
                T2.*
                ,T0.BP
                ,T0.PortOfLoading
                ,T0.DestinationPort
                ,T0.Cargo AS VesselName
                ,T0.Shipement AS Shipment
                ,T0.Shipement_no
                ,CONVERT(VARCHAR, T0.Vessel_arrive, 106) AS VesselArrival
                ,CONVERT(VARCHAR, T0.VesselDeparture, 106) AS VesselDeparture
                ,CONVERT(VARCHAR, T0.UnloadingDate, 106) AS UnloadingDate
                ,CONVERT(VARCHAR, T0.FinishUnloadingDate, 106) AS FinishUnloadingDate
                ,T3.name AS Created_by
                ,T0.Updated_by
                --,T0.created_at
                --,T0.updated_at
                --,CONVERT(VARCHAR, T0.PostingDate, 106) AS PostingDate
                --,CONCAT(YEAR(T2.PeriodDate), '-', MONTH(T2.PeriodDate)) as PeriodDate
                ,CASE
                    WHEN T2.Status = 'Open' THEN '#CFD8DC'
                    WHEN T2.Status = 'Cancel' THEN 'red'
                    WHEN T2.Status = 'Closed' THEN '#43A047'
                END AS Color
                --,T0.Remarks
                ,T0.Voyage
            ")
            ->leftJoin('T_MDOC_Header AS T0', 'T0.DocEntry', 'T2.ImportID')
            ->leftJoin('users AS T3', 'T3.id', 'T2.CreatedBy')
            ->when($permission_id, function ($query) use ($select_data, $search, $permission_id, $filter, $sorts, $order, $data_status) {
                if ($data_status == 'All') {
                    $data_query = $query->whereRaw(
                        "T2.Status IN ('Open', 'Open-upload')"
                    );
                } else {
                    $data_query = $query->whereRaw(
                        "T2.Status LIKE '%${data_status}'"
                    );
                }

                if ($select_data == 'BL') {
                    $data_query = $query->whereRaw(
                        "T0.DocEntry IN ( SELECT X0.DocNum
                            FROM T_MDOC AS X0
                            WHERE X0.No_bl LIKE '%${search}%'
                        )"
                    );
                }

                if ($select_data == 'DocNum') {
                    $data_query = $query->whereRaw(
                        "T2.DocNum LIKE '%${search}%'"
                    );
                }

                if ($select_data == 'Vessel Name') {
                    $data_query = $query->whereRaw(
                        "T0.Cargo LIKE '%${search}%'"
                    );
                }
                if ($select_data == 'Tenant Name') {
                    $data_query = $query->whereRaw(
                        "(
                            select count(*)
                            from M_Tenant as t
                            left join BEXP as b on t.DocEntry = b.TenantId
                            where t.Name LIKE '%${search}%'
                            and b.DocNum = T2.DocEntry
                            and b.Type = 'Import'
                        ) > 0"
                    );
                }
                if ($select_data == 'Shipment') {
                    $data_query = $query->whereRaw(
                        "T0.Shipement LIKE '%${search}%'"
                    );
                }

                if ($select_data && $select_data != 'BL' && $select_data != 'Tenant Name' && $select_data != 'DocNum' && $select_data != 'Vessel Name' && $select_data != 'Shipment' && $search) {
                    $data_query = $query->whereRaw("
                        T0.${select_data} LIKE '%${search}%'
                    ");
                }

                if ($filter == 'All') {
                    $data_query = $query->whereRaw(
                        "CONCAT('20',LEFT(T0.DocNum,2)) LIKE '%%'"
                    );
                } else {
                    $data_query = $query->whereRaw(
                        "CONCAT('20',LEFT(T0.DocNum,2)) LIKE '%${filter}%'"
                    );
                }

                if ($permission_id != '1') {
                    $data_query = $query->where('T0.Deleted', '=', 'N');
                }

                if ($sorts == 'Shipment') {
                    $data_query = $query->orderBy(DB::raw('YEAR(Vessel_arrive)'), 'DESC')
                        ->orderBy(DB::raw("
                            CASE ISNUMERIC(T0.[Shipement])
	                            WHEN 1 THEN REPLICATE('0', 100 - LEN(T0.[Shipement])) + T0.[Shipement]
	                            ELSE T0.[Shipement]
                            END
                        "), 'DESC');
                } elseif ($sorts == 'DocNum') {
                    $data_query = $query->orderBY('T2.DocNum', $order);
                } else {
                    $data_query = $query->orderBY($sorts, $order);
                }
                return $data_query;
            });

        $result['total'] = $query->count();
        $import = $query->paginate($row_data)
            ->items();

        $year_shipments = DB::table('T_MDOC_Header')
            ->selectRaw('YEAR(Vessel_arrive) AS Year')
            ->orderBy(DB::raw('YEAR(Vessel_arrive)'), 'DESC')
            ->distinct()
            ->get();

        $year_arr = [];
        foreach ($year_shipments as $year_shipment) {
            $year_arr[] = [
                'Name' => $year_shipment->Year,
            ];
        }

        $filter = array_merge([['Name' => 'All']], $year_arr);

        $status = DB::table('BHIMP')
            ->distinct()
            ->select("Status")
            ->pluck('Status')
            ->toArray();

        $item_search = [
            'BL',
            'Shipment',
            'DocNum',
            'Vessel Name',
            'Voyage',
            'Tenant Name'
        ];

        $result = array_merge($result, [
            'rows' => $import,
            'filter' => $filter,
            'status' => array_merge(['All'], $status),
            'item_search' => $item_search,
            'year_local' => $year_local
        ]);
        return response()->json($result);
    }

    /**
     * @param Request $request
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function groupData(Request $request)
    {
        $details = collect($request->details);
        $form = $request->form;
        $group_item_qty = [];
        $not_group_item = [];
        $first_group = [];
        $array_base_id = [];
        foreach ($details as $detail) {
            if (key_exists('IsGroup', $detail)) {
                if ($detail['IsGroup'] == 'Y') {
                    $group_item_qty[] = $detail['LoadingQty'];
                    $array_base_id[] = $detail['BaseId'];
                    $first_group = $detail;
                }
            } else {
                $not_group_item[] = $detail;
            }
        }
        $group_item_new[] = [
            'Tenant' => $first_group['Tenant'],
            'AJU_No' => key_exists('AJU_No', $first_group) ? $first_group['AJU_No'] : '',
            //'Attachment' => $first_group['Attachment'],
            'Agent' => $first_group['Agent'],
            'BaseIdString' => implode(',', $array_base_id),
            'BCType' => $first_group['BCType'],
            'BP' => $first_group['BP'],
            'CompanyHeader' => $first_group['CompanyHeader'],
            'CountDetails' => $first_group['CountDetails'],
            //'Created_by' => $first_group['Created_by'],
            'CurrencyPortService' => $first_group['CurrencyPortService'],
            'CurrencyServiceLoading' => $first_group['CurrencyServiceLoading'],
            'DateBL' => null,
            'DocNum' => $first_group['DocNum'],
            'Ebilling_date' => key_exists('Ebilling_date', $first_group) ? $first_group['Ebilling_date'] : '',
            'Jetty' => $first_group['Jetty'],
            'LoadingItem' => $first_group['LoadingItem'],
            'LoadingQty' => array_sum($group_item_qty),
            'NoBL' => '-',
            'Notification' => $first_group['Notification'],
            'PortServiceType' => $first_group['PortServiceType'],
            'Price' => $first_group['Price'],
            'REG_No' => key_exists('REG_No', $first_group) ? $first_group['REG_No'] : '',
            'REG_date' => key_exists('REG_date', $first_group) ? $first_group['REG_date'] : '',
            'Remarks' => key_exists('Remarks', $first_group) ? $first_group['Remarks'] : '',
            'SPE_No' => key_exists('SPE_No', $first_group) ? $first_group['SPE_No'] : '',
            'ServiceLoading' => $first_group['ServiceLoading'],
            'Status' => $first_group['Status'],
            'WeightCategory' => $first_group['WeightCategory'],
            'LoadingUnloadingType' => 'Yes',
        ];

        return response()->json([
            'rows' => array_merge($not_group_item, $group_item_new),
        ]);
    }

    /**
     * @param Request $request
     * @param $docNum
     *
     * @return bool|\Illuminate\Http\JsonResponse
     */
    public function dataBilling(Request $request, $docNum)
    {
        // $index = $this->getPrevAndNext($docNum, 'BHIMP', 'DocEntry');
        $query_header = DB::table('BHIMP AS T2')
            ->selectRaw("
                    T2.*
                    ,T0.Cargo AS VesselName
                    ,CASE
                        WHEN CAST(T0.Shipement AS NVARCHAR) = '0' THEN '-'
                        ELSE T0.Shipement
                    END AS Shipment
                    ,CONVERT(varchar,T0.Vessel_arrive,20) as VesselArrival
                    ,CONVERT(varchar,T0.FinishUnloadingDate,20) as FinishUnloadingDate
                    ,CONVERT(varchar,T0.UnloadingDate,20) as UnloadingDate
                    ,CONVERT(varchar,T0.AnchorageDate,20) as AnchorageDate
                    ,CONVERT(varchar,T0.BerthingDate,20) as BerthingDate
                    ,CONVERT(varchar,T0.VesselDeparture,20) as VesselDeparture
                    ,T0.PortOfLoading AS PortOrigin
                    ,T0.DestinationPort
                    ,CONVERT(VARCHAR, T0.PostingDate, 106) AS PostingDate
                    ,T0.Remarks
                    ,T0.Voyage
                    ,T2.ImportID AS ExportId
                    ,T0.GrossWeight
                    ,T0.VesselFlag
                    ,T0.DocStatus
                    ,T2.Status
                    ,T0.DocEntry AS VesselDocEntry
                    ,T0.DocNum AS VesselDocNum
                    --,T2.ExportID as ExportId
                    ,T3.Name As Jetty
                    ,T3.Port
                ")
            ->leftJoin('T_MDOC_Header AS T0', 'T0.DocEntry', 'T2.ImportID')
            ->leftJoin('M_Jetty AS T3', 'T3.DocEntry', 'T2.Jetty')
            ->whereRaw("T2.DocEntry='${docNum}'")
            ->first();

        $permission_id = $request->user()->permission_id;
        $user_id = $request->user()->id;
        $open_time = str_replace('.', '', microtime(true));
        session(['openDateTime' => $open_time]);

        if ($query_header) {
            $query = DB::table('BEXP AS T0')
                ->leftJoin('M_TBC AS T1', 'T1.DocEntry', 'T0.BCType')
                ->leftJoin('M_Tenant AS T2', 'T2.DocEntry', 'T0.TenantId')
                ->leftJoin('users AS T3', 'T3.id', 'T0.CreatedBy')
                ->leftJoin('M_BP AS T4', 'T4.DocEntry', 'T0.BP')
                ->leftJoin('M_Jetty AS T5', 'T5.DocEntry', 'T0.Jetty')
                ->leftJoin('M_Agent AS T6', 'T6.DocEntry', 'T0.Agent')
                ->selectRaw("
                    T0.*
                    ,T2.Name AS Tenant
                    ,T2.FullName AS TenantFullName
                    ,T2.IsTenant
                    ,T4.Name AS BP
                    ,T6.Name AS Agent
                    ,T1.Type AS BCType
                    ,CONVERT(VARCHAR, T0.DateBL, 105) AS DateBL
                    ,CAST('0'+CAST(T0.LoadingQty AS DECIMAL(20, 5)) AS NVARCHAR) as LoadingQty
                    ,(
                        SELECT distinct CONCAT(ItemCode, ' - ' , FrgnName) FROM MPS WHERE ItemCode=T0.PortServiceType
                    ) AS PortServiceType
                    ,CAST('0'+CAST(T0.Total AS DECIMAL(20, 5)) AS NVARCHAR) AS Total
                    ,(
                        SELECT COUNT(*) FROM BEXP1 as x
                        WHERE x.ExportId = T0.DocEntry
                    )AS CountDetails
                    ,T5.Name As Jetty
                    ,T5.Port
                ")
                ->where('T0.DocNum', '=', $docNum)
                ->where('T0.Type', '=', 'Import')
                ->where('T0.Deleted', '=', 'N')
                ->orderBy('T0.BaseId')
                ->orderBy('T0.DocEntry')
                ->get();

            $open_date_time = str_replace('.', '', microtime(true));

            return response()->json([
                'header' => $query_header,
                'rows' => $query,
                'openDateTime' => $open_date_time,
                'docEntry' => $docNum,
                // 'prev' => $index ? $index->prev : 0,
                // 'next' => $index ? $index->next : 0,
                'runCBMValidation' => 'Y',
            ]);
        }
        return false;
    }

    /**
     * @param Request $request
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function dataCargo(Request $request)
    {
        $year_now = date('Y');
        $month_now = date('m');
        $vessel = DB::table('T_MDOC_Header AS T1')
            ->select('T1.Cargo AS Name')
            // ->where("T1.DocStatus", "=", "Open")
            ->whereRaw("YEAR(T1.Reportdate) = ${year_now}")
            // ->whereRaw("CONCAT('20',LEFT(T1.DocNum,2)) >= ${year_now}")
            ->whereRaw("(
                CASE
                    WHEN LEN(T1.VesselDeparture) > 0 THEN 'Yes'
                    ELSE 'No'
                END
            ) = 'Yes' ")
            ->whereRaw("T1.DocEntry NOT IN (SELECT ImportId FROM BHIMP
                WHERE Status = 'Open' OR Status = 'Open-upload')")
            ->distinct()
            ->get();

        return response()->json([
            'rows' => $vessel,
        ]);
    }

    /**
     * @param Request $request
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function voyageBYVessel(Request $request)
    {
        $year_now = date('Y');
        $month_now = date('m');
        $voyage = DB::table('T_MDOC_Header AS T1')
            ->select('T1.Voyage AS Name')
            ->where('T1.Cargo', '=', $request->vessel)
            ->whereRaw("YEAR(T1.Reportdate) = ${year_now}")
            // ->whereRaw("CONCAT('20',LEFT(T1.DocNum,2)) >= ${year_now}")
            ->whereRaw("T1.DocEntry NOT IN (SELECT ImportId FROM BHIMP WHERE Status = 'Open' OR Status = 'Open-upload')")
            // ->where("T1.DocStatus", "=", "Open")
            ->distinct()
            ->get();

        return response()->json([
            'rows' => $voyage,
        ]);
    }

    public function getPartial(Request $request)
    {
        $billing_id = $request->billingId;
        $base_id = $request->arrayBaseId;
        $query = DB::table("BHIMP AS T0")
            ->leftJoin("T_MDOC_Header AS T1", "T0.ImportId", "T1.DocEntry")
            ->leftJoin("T_MDOC AS T2", "T1.DocEntry", "T2.DocNum")
            ->leftJoin('M_Tenant AS T3', 'T3.DocEntry', 'T2.Tenant_key')
            ->leftJoin('M_BP AS T4', 'T4.DocEntry', 'T2.BPNum')
            ->leftJoin('M_Agent AS T5', 'T5.DocEntry', 'T2.Agent')
            ->leftJoin('M_Jetty AS T6', 'T6.DocEntry', 'T1.Jetty')
            ->where("T2.DocType", "=", "Import")
            ->where("T2.Deleted", "=", "N")
            ->whereNotIn("T2.DocEntry", $base_id)
            ->whereRaw("T2.DocEntry NOT IN (SELECT BaseId FROM BEXP WHERE DocNum = T0.DocEntry AND Type='Import')")
            ->where("T0.DocEntry", "=", $billing_id)
            ->selectRaw("
                CAST ('0'+CAST(
                        CASE
                            WHEN UPPER(T2.UnitWeight) = 'KGS'  THEN T2.GrossWeight / 1000
                            WHEN UPPER(T2.UnitWeight) = 'KG' THEN T2.GrossWeight / 1000
                            ELSE T2.GrossWeight
                        END AS DECIMAL(20, 5)
                    ) AS NVARCHAR) as LoadingQty
                ,ROW_NUMBER() OVER (Order by T2.DocEntry) AS RowNumber
                ,T2.ItemName AS LoadingItem
                ,T6.Name As Jetty
                ,T3.Name AS Tenant
                ,T4.Name AS BP
                ,T5.Name AS Agent
                ,T2.No_bl AS NoBL
                ,T1.Type AS BCType
                ,CONVERT(VARCHAR, T2.Date_bl, 105) AS DateBL
                ,T2.AJU_No
                ,T2.ItemName AS LoadingItem
                ,T2.UnitWeight
                ,T2.REG_No
                ,T2.SPPB_no AS SPE_No
                ,NULL AS DocEntry
                ,(
                    SELECT TOP 1 BilledBy FROM MPLTENANT WHERE TenantKey = T2.Tenant_key
                ) AS CompanyHeader
                , 'Yes' AS LoadingUnloadingType
                , 'B/L Weight' AS WeightCategory
                ,CONVERT(VARCHAR, T2.REG_date, 105) AS REG_date
                ,CONVERT(VARCHAR, T2.Ebilling_date, 105) AS Ebilling_date
                ,'attachment' AS Attachment
                ,CONVERT(VARCHAR, T2.SPPB_date, 105) AS SPE_date
                ,T2.Created_by
                ,NULL AS Notification
                ,T2.Remarks
                ,0 AS CountDetails
                ,'Open' AS Status
                ,T2.DocNum
                ,T2.DocEntry As BaseId
                ,CASE
                    WHEN (SELECT COUNT(*) FROM MPLSL
                            WHERE TenantKey = T2.Tenant_key AND Type ='Tenant'
                            AND CONVERT(varchar,T1.VesselDeparture,20) >= PeriodStart AND CONVERT(varchar,T1.VesselDeparture,20) <= PeriodEnd) > 0
                        THEN (
                            SELECT Top 1 Price
                            FROM MPLSL
                            WHERE TenantKey=T2.Tenant_key AND Type ='Tenant'
                            AND CONVERT(varchar,T1.VesselDeparture,20) >= PeriodStart AND CONVERT(varchar,T1.VesselDeparture,20) <= PeriodEnd
                        )
                    ELSE
                        (
                            SELECT Top 1 Price
                            FROM MPLSL
                            WHERE Type ='Global'
                            AND CONVERT(varchar,T1.VesselDeparture,20) >= PeriodStart AND CONVERT(varchar,T1.VesselDeparture,20) <= PeriodEnd
                        )
                END AS ServiceLoading
                ,CASE
                    WHEN (SELECT COUNT(*) FROM MPLPS
                            WHERE TenantKey = T2.Tenant_key AND Type ='Tenant'
                            AND CONVERT(varchar,T1.VesselDeparture,20) >= PeriodStart AND CONVERT(varchar,T1.VesselDeparture,20) <= PeriodEnd
                            ) > 0
                        THEN (
                            SELECT Top 1 Price
                            FROM MPLPS
                            WHERE TenantKey=T2.Tenant_key AND Type ='Tenant'
                            AND CONVERT(varchar,T1.VesselDeparture,20) >= PeriodStart AND CONVERT(varchar,T1.VesselDeparture,20) <= PeriodEnd

                        )
                    ELSE
                        0
                END AS Price
                ,CASE
                    WHEN (SELECT COUNT(*) FROM MPLSL
                            WHERE TenantKey = T2.Tenant_key AND Type ='Tenant'
                            AND CONVERT(varchar,T1.VesselDeparture,20) >= PeriodStart AND CONVERT(varchar,T1.VesselDeparture,20) <= PeriodEnd) > 0
                        THEN (
                            SELECT Top 1 B.Currency
                            FROM MPLSL AS A
                            LEFT JOIN M_Currency AS B ON A.Currency = B.DocEntry
                            WHERE A.TenantKey=T2.Tenant_key AND A.Type ='Tenant'
                            AND CONVERT(varchar,T1.VesselDeparture,20) >= A.PeriodStart AND CONVERT(varchar,T1.VesselDeparture,20) <= A.PeriodEnd
                        )
                    ELSE
                        (
                            SELECT Top 1 B.Currency
                            FROM MPLSL AS A
                            LEFT JOIN M_Currency AS B ON A.Currency = B.DocEntry
                            WHERE  A.Type ='Global'
                            AND CONVERT(varchar,T1.VesselDeparture,20) >= A.PeriodStart AND CONVERT(varchar,T1.VesselDeparture,20) <= A.PeriodEnd
                        )
                END AS CurrencyServiceLoading
                ,CASE
                    WHEN (SELECT COUNT(*) FROM MPLPS
                            WHERE TenantKey = T2.Tenant_key AND Type ='Tenant'
                            AND CONVERT(varchar,T1.VesselDeparture,20) >= PeriodStart AND CONVERT(varchar,T1.VesselDeparture,20) <= PeriodEnd
                            ) > 0
                        THEN (
                            SELECT Top 1 B.Currency
                            FROM MPLPS AS A
                            LEFT JOIN M_Currency AS B ON A.Currency = B.DocEntry
                            WHERE A.TenantKey=T2.Tenant_key AND A.Type ='Tenant'
                            AND CONVERT(varchar,T1.VesselDeparture,20) >= A.PeriodStart AND CONVERT(varchar,T1.VesselDeparture,20) <= A.PeriodEnd

                        )
                    ELSE
                        ''
                END AS CurrencyPortService
                ,CASE
                    WHEN (SELECT COUNT(*) FROM MPLPS
                            WHERE TenantKey = T2.Tenant_key AND Type ='Tenant'
                            AND CONVERT(varchar,T1.VesselDeparture,20) >= PeriodStart AND CONVERT(varchar,T1.VesselDeparture,20) <= PeriodEnd) > 0
                        THEN (
                            SELECT Top 1 CONCAT(B.ItemCode, ' - ', B.FrgnName)
                            FROM MPLPS AS A
                            LEFT JOIN MPS AS B ON B.DocEntry = A.PSKey
                            WHERE A.TenantKey=T2.Tenant_key AND A.Type ='Tenant'
                            AND CONVERT(varchar,T1.VesselDeparture,20) >= A.PeriodStart AND CONVERT(varchar,T1.VesselDeparture,20) <= A.PeriodEnd
                        )
                    ELSE
                        ''
                END AS PortServiceType

                --,T0.DocStatus
                , 'Guo Yao Min' AS Signature1
            ")
            ->get();
        return response()->json([
            'rows' => $query
        ]);
    }

    /**
     * @param Request $request
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getExportData(Request $request)
    {
        $query_header = DB::table('T_MDOC_Header AS T0')
            ->selectRaw("
                    T0.DocEntry AS ExportId
                    ,T0.DocNum
                    ,T0.Cargo AS VesselName
                    ,CASE
                        WHEN CAST(T0.Shipement AS NVARCHAR) = '0' THEN '-'
                        ELSE T0.Shipement
                    END AS Shipment
                    ,CONVERT(varchar,T0.Vessel_arrive,20) as VesselArrival
                    ,CONVERT(varchar,T0.FinishUnloadingDate,20) as FinishUnloadingDate
                    ,CONVERT(varchar,T0.UnloadingDate,20) as UnloadingDate
                    ,CONVERT(varchar,T0.AnchorageDate,20) as AnchorageDate
                    ,CONVERT(varchar,T0.BerthingDate,20) as BerthingDate
                    ,CONVERT(varchar,T0.VesselDeparture,20) as VesselDeparture
                    ,T0.Created_by
                    ,T0.Updated_by
                    ,T0.created_at
                    ,T0.updated_at
                    ,T0.PortOfLoading AS PortOrigin
                    ,ISNULL(T0.DestinationPort, NULL ) AS DestinationPort
                    ,CONVERT(VARCHAR, T0.PostingDate, 106) AS PostingDate
                    ,T0.Remarks
                    ,T0.Voyage
                    ,T0.GrossWeight
                    ,T0.VesselFlag
                    ,T0.DocStatus
                    ,T3.Name As Jetty
                    ,T3.Port
                    ,'Import' AS DocumentType
                ")
            ->leftJoin('M_Jetty AS T3', 'T3.DocEntry', 'T0.Jetty')
            ->where('T0.Cargo', '=', $request->vessel)
            ->where('T0.Voyage', '=', $request->voyage)
            ->whereRaw("T0.DocEntry NOT IN (SELECT ImportId FROM BHIMP WHERE Status = 'Open' OR Status = 'Open-upload')")
            ->first();

        if ($query_header) {
            $filed_jetty = $query_header->Jetty;
            $port = $query_header->Port;
            $vessel_arrive = $query_header->VesselDeparture;

            $query = DB::table('T_MDOC AS T0')
                ->leftJoin('M_TBC AS T1', 'T1.DocEntry', 'T0.BC_type_key')
                ->leftJoin('M_Tenant AS T2', 'T2.DocEntry', 'T0.Tenant_key')
                ->leftJoin('users AS T3', 'T3.id', 'T0.Created_id')
                ->leftJoin('M_BP AS T4', 'T4.DocEntry', 'T0.BPNum')
                ->leftJoin('M_Agent AS T5', 'T5.DocEntry', 'T0.Agent')
                ->whereNotIn("T2.Name", ["BDM"])
                ->selectRaw("
                    T2.Name AS Tenant
                    ,T4.Name AS BP
                    ,T5.Name AS Agent
                    ,T0.No_bl AS NoBL
                    ,T1.Type AS BCType
                    ,CONVERT(VARCHAR, T0.Date_bl, 105) AS DateBL
                    ,T0.AJU_No
                    ,T0.ItemName AS LoadingItem
                    ,T0.UnitWeight
                    ,T0.REG_No
                    ,T0.SPPB_no AS SPE_No
                    ,CAST ('0'+CAST(
                        CASE
                            WHEN UPPER(T0.UnitWeight) = 'KGS'  THEN T0.GrossWeight / 1000
                            WHEN UPPER(T0.UnitWeight) = 'KG' THEN T0.GrossWeight / 1000
                            ELSE T0.GrossWeight
                        END AS DECIMAL(20, 5)
                    ) AS NVARCHAR) as LoadingQty
                    ,(
                        SELECT COUNT(*) FROM BEXP1 as x
                        WHERE x.ExportId = T0.DocEntry AND x.Type='Import'
                    )AS CountDetails
                    ,(
                        SELECT TOP 1 BilledBy FROM MPLTENANT WHERE TenantKey = T0.Tenant_key
                    ) AS CompanyHeader
                    , 'Yes' AS LoadingUnloadingType
                    , 'B/L Weight' AS WeightCategory
                    ,CONVERT(VARCHAR, T0.REG_date, 105) AS REG_date
                    ,CONVERT(VARCHAR, T0.Ebilling_date, 105) AS Ebilling_date
                    ,'attachment' AS Attachment
                    ,CONVERT(VARCHAR, T0.SPPB_date, 105) AS SPE_date
                    ,T0.Created_by
                    ,NULL AS Notification
                    ,T0.Remarks
                    ,0 AS CountDetails
                    ,'Open' AS Status
                    ,'${filed_jetty}' AS Jetty
                    ,'${port}' AS Port
                    ,T0.DocNum
                    ,T0.DocEntry As BaseId
                    ,NULL AS DocEntry
                    ,CASE
                        WHEN (SELECT COUNT(*) FROM MPLSL
                                WHERE TenantKey = T0.Tenant_key AND Type ='Tenant'
                                AND '${vessel_arrive}' >= PeriodStart AND '${vessel_arrive}' <= PeriodEnd) > 0
                            THEN (
                                SELECT Top 1 Price
                                FROM MPLSL
                                WHERE TenantKey=T0.Tenant_key AND Type ='Tenant'
                                AND '${vessel_arrive}' >= PeriodStart AND '${vessel_arrive}' <= PeriodEnd
                            )
                        ELSE
                            (
                                SELECT Top 1 Price
                                FROM MPLSL
                                WHERE Type ='Global'
                                AND '${vessel_arrive}' >= PeriodStart AND '${vessel_arrive}' <= PeriodEnd
                            )
                    END AS ServiceLoading
                    ,CASE
                        WHEN (SELECT COUNT(*) FROM MPLPS
                                WHERE TenantKey = T0.Tenant_key AND Type ='Tenant'
                                AND '${vessel_arrive}' >= PeriodStart AND '${vessel_arrive}' <= PeriodEnd
                                ) > 0
                            THEN (
                                SELECT Top 1 Price
                                FROM MPLPS
                                WHERE TenantKey=T0.Tenant_key AND Type ='Tenant'
                                AND '${vessel_arrive}' >= PeriodStart AND '${vessel_arrive}' <= PeriodEnd

                            )
                        ELSE
                            0
                    END AS Price
                    ,CASE
                        WHEN (SELECT COUNT(*) FROM MPLSL
                                WHERE TenantKey = T0.Tenant_key AND Type ='Tenant'
                                AND '${vessel_arrive}' >= PeriodStart AND '${vessel_arrive}' <= PeriodEnd) > 0
                            THEN (
                                SELECT Top 1 B.Currency
                                FROM MPLSL AS A
                                LEFT JOIN M_Currency AS B ON A.Currency = B.DocEntry
                                WHERE A.TenantKey=T0.Tenant_key AND A.Type ='Tenant'
                                AND '${vessel_arrive}' >= A.PeriodStart AND '${vessel_arrive}' <= A.PeriodEnd
                            )
                        ELSE
                            (
                                SELECT Top 1 B.Currency
                                FROM MPLSL AS A
                                LEFT JOIN M_Currency AS B ON A.Currency = B.DocEntry
                                WHERE  A.Type ='Global'
                                AND '${vessel_arrive}' >= A.PeriodStart AND '${vessel_arrive}' <= A.PeriodEnd
                            )
                    END AS CurrencyServiceLoading
                    ,CASE
                        WHEN (SELECT COUNT(*) FROM MPLPS
                                WHERE TenantKey = T0.Tenant_key AND Type ='Tenant'
                                AND '${vessel_arrive}' >= PeriodStart AND '${vessel_arrive}' <= PeriodEnd
                                ) > 0
                            THEN (
                                SELECT Top 1 B.Currency
                                FROM MPLPS AS A
                                LEFT JOIN M_Currency AS B ON A.Currency = B.DocEntry
                                WHERE A.TenantKey=T0.Tenant_key AND A.Type ='Tenant'
                                AND '${vessel_arrive}' >= A.PeriodStart AND '${vessel_arrive}' <= A.PeriodEnd

                            )
                        ELSE
                            ''
                    END AS CurrencyPortService
                    ,CASE
                        WHEN (SELECT COUNT(*) FROM MPLPS
                                WHERE TenantKey = T0.Tenant_key AND Type ='Tenant'
                                AND '${vessel_arrive}' >= PeriodStart AND '${vessel_arrive}' <= PeriodEnd) > 0
                            THEN (
                                SELECT Top 1 CONCAT(B.ItemCode, ' - ', B.FrgnName)
                                FROM MPLPS AS A
                                LEFT JOIN MPS AS B ON B.DocEntry = A.PSKey
                                WHERE A.TenantKey=T0.Tenant_key AND A.Type ='Tenant'
                                AND '${vessel_arrive}' >= A.PeriodStart AND '${vessel_arrive}' <= A.PeriodEnd
                            )
                        ELSE
                            ''
                    END AS PortServiceType

                    --,T0.DocStatus
                    , 'Guo Yao Min' AS Signature1
                ")
                ->where('T0.DocNum', '=', $query_header->ExportId)
                ->where('T0.Deleted', '=', 'N')
                ->where('T0.DocType', '=', 'Import')
                ->orderBy("T0.DocEntry")
                ->get();

            return response()->json([
                'header' => $query_header,
                'rows' => $query,
            ]);
        }
    }

    /**
     * @return \Illuminate\Http\JsonResponse
     */
    public function getPortService(Request $request)
    {
        // Import Helper
        return $this->dataPortService($request);
    }

    /**
     * @param $docNum
     *
     * @return int
     */
    public function checkDoubleDoc($docNum)
    {
        return DB::table('BHIMP')
            ->where('ImportID', '=', $docNum)
            ->where('Status', '=', 'Open')
            ->count();
    }

    /**
     * @return \Illuminate\Http\JsonResponse
     */
    public function fetchDocNum()
    {
        $doc_num = DB::table('BHIMP')->select('DocNum')
            ->orderBY('DocNum', 'DESC')
            ->first();

        $document = $doc_num ? (int) $doc_num->DocNum + 1 : $this->generateDocNum(Carbon::now());

        return response()->json([
            'DocNum' => $document,
        ]);
    }

    /**
     * @param $sysDate
     *
     * @return string
     */
    public function generateDocNum($sysDate)
    {
        $data_date = strtotime($sysDate);
        $year_val = date('y', $data_date);
        $full_year = date('Y', $data_date);
        $month = date('m', $data_date);
        $day_val = date('j', $data_date);
        $end_date = date('t', $data_date);

        if ($day_val == 1) {
            $doc_num = DB::table('BHIMP')
                ->selectRaw('ISNULL(DocNum, 0) as DocNum')
                ->whereBetween(DB::raw('convert(varchar, created_at, 23)'), ["${full_year}-${month}-01", "${full_year}-${month}-${end_date}"])
                ->orderBy('created_at', 'DESC');
            if ($doc_num->count() > 0) {
                $doc_num = $doc_num->first();
                $number = !$doc_num ? '00000000' : $doc_num->DocNum;
                $clear_doc_num = (int) substr($number, 4, 7);
                $number = $clear_doc_num + 1;
                return $year_val . $month . sprintf('%04s', $number);
            }

            return $year_val . $month . sprintf('%04s', '1');
        }
        $doc_num = DB::table('BHIMP')
            ->selectRaw('ISNULL(DocNum, 0) as DocNum')
            ->whereBetween(DB::raw('convert(varchar, created_at, 23)'), ["${full_year}-${month}-01", "${full_year}-${month}-${end_date}"])
            ->orderBy('created_at', 'DESC')
            ->first();

        $number = !$doc_num ? '00000000' : $doc_num->DocNum;
        $clear_doc_num = (int) substr($number, 4, 7);
        $number = $clear_doc_num + 1;
        return $year_val . $month . sprintf('%04s', $number);
    }

    /**
     * @param Request $request
     * @param $id
     *
     * @return \Illuminate\Http\JsonResponse
     *
     * @throws \Exception
     */
    public function store(Request $request, $id)
    {
        if (empty($request->form['PortService']) || empty($request->form['Jetty'])) {
            return response()->json([
                'errors' => true,
                'message' => 'Port Service and Jetty cannot empty!',
            ], 422);
        }

        if (empty($request->form['VesselDeparture'])) {
            return response()->json([
                'errors' => true,
                'message' => 'Vessel Departure cannot empty!',
            ], 422);
        }

        if (empty($request->form['PeriodDate'])) {
            return response()->json([
                'errors' => true,
                'message' => 'Period Date Cannot Empty!',
            ], 422);
        }

        if (array_key_exists('ExportId', $request->form)) {
            if ($request->form['ExportId']) {
                if ($this->checkDoubleDoc($request->form['ExportId']) > 0) {
                    DB::table('BHIMP')
                        ->whereRaw('
                            (
                                SELECT COUNT(*) FROM BEXP WHERE DocNum = BHIMP.DocEntry
                            ) = 0
                        ')
                        ->where('Status', '=', 'Open')
                        ->delete();

                    // return response()->json([
                    //    "errors" => true,
                    //    "message" => "Double input for this data export! Please use other data"
                    // ]);
                }
            }
        }

        $details = collect($request->details);

        if ($details) {
            $all_fill = [];
            foreach ($details as $index_bl => $items) {
                if (!array_key_exists('CompanyHeader', $items)) {
                    $lines = $index_bl + 1;
                    return response()->json([
                        'errors' => true,
                        'message' => "Line ${lines}: Company header cannot empty!",
                    ], 422);
                }

                if (!array_key_exists('Agent', $items)) {
                    $lines = $index_bl + 1;
                    return response()->json([
                        'errors' => true,
                        'message' => "Line ${lines}: Agent cannot empty!",
                    ], 422);
                }

                if (!array_key_exists('Classification', $items)) {
                    $lines = $index_bl + 1;
                    return response()->json([
                        'errors' => true,
                        'message' => "Line ${lines}: Classifications cannot empty!",
                    ], 422);
                }

                if (empty($items['Classification'])) {
                    $lines = $index_bl + 1;
                    return response()->json([
                        'errors' => true,
                        'message' => "Line ${lines}: Classification cannot empty!",
                    ], 422);
                }

                if (empty($items['CompanyHeader'])) {
                    $lines = $index_bl + 1;
                    return response()->json([
                        'errors' => true,
                        'message' => "Line ${lines}: Company header cannot empty!",
                    ], 422);
                }

                if (empty($items['Agent'])) {
                    $lines = $index_bl + 1;
                    return response()->json([
                        'errors' => true,
                        'message' => "Line ${lines}: Agent cannot empty!",
                    ], 422);
                }

                if (empty($items['Tenant'])) {
                    $lines = $index_bl + 1;
                    return response()->json([
                        'errors' => true,
                        'message' => "Line ${lines}: Tenant cannot empty!",
                    ], 422);
                }

                if (!array_key_exists('Tenant', $items)) {
                    $lines = $index_bl + 1;
                    return response()->json([
                        'errors' => true,
                        'message' => "Line ${lines}: Tenant cannot empty!",
                    ], 422);
                }
            } // Details
        }

        $this->validatePostingPeriod($request->form['PeriodDate']);


        DB::beginTransaction();
        try {
            // get header
            $header = $this->getHeaderDoc($id, $request);
            // set created at
            $created = !empty($header) ? $header->created_at : Carbon::now();
            // process header
            $doc_num = $this->processHeaderDoc($header, $created, $request);

            if ($doc_num) {
                if ($details) {
                    $all_fill = [];
                    foreach ($details as $index_bl => $items) {
                        $last_data = key_exists('DocEntry', $items) ? $items['DocEntry'] : null;
                        $index = $doc_num;
                        $port_service = array_key_exists('PortServiceType', $items) ?
                            substr($items['PortServiceType'], 0, 7) :
                            substr($request->form['PortService'], 0, 7);
                        $monitor_doc = BEXP::where('DocEntry', '=', $last_data)->first();
                        $check_ps = $this->checkPortSeviceExist(
                            $port_service,
                            $request->form['VesselArrival'],
                            $items['Tenant']
                        );

                        $check_sl = $this->checkServiceLoadingExist(
                            $request->form['VesselArrival'],
                            $items['Tenant']
                        );

                        if ($check_ps == 'tenant') {
                            return response()->json([
                                'errors' => true,
                                'message' => "Please Define Price List Port Service
                                ${port_service} For Tenant " . $items['Tenant'] . ' In Range Lower than
                                and greater than ' . $request->form['VesselArrival'],
                            ], 422);
                        }
                        if ($check_ps == 'global') {
                            return response()->json([
                                'errors' => true,
                                'message' => "Please Define Price List For Port Service ${port_service}
                                In Range Lower than and greater than " . $request->form['VesselArrival'],
                            ], 422);
                        }
                        if ($check_sl == 'tenant') {
                            return response()->json([
                                'errors' => true,
                                'message' => 'Please Define Price List Service Loading For
                                    Tenant ' . $items['Tenant'] . ' In Range Lower than and greater than ' .
                                    $request->form['VesselArrival'],
                            ], 422);
                        }
                        if ($check_sl == 'global') {
                            return response()->json([
                                'errors' => true,
                                'message' => 'Please Define Price List For Service Loading
                                        In Range Lower than and greater than ' . $request->form['VesselArrival'],
                            ], 422);
                        }
                        // $doc_entry = $this->saveData($index_bl, $items, $request, $doc_num);
                        $doc_entry = $this->storeBilling($index_bl, $items, $request, $doc_num, 'Import');

                        if ($doc_entry) {
                            $all_fill[] = true;
                            //$this->reportBilling($request, $doc_entry);
                        } else {
                            $all_fill[] = true;
                        }
                    } // Details
                    if (!in_array(false, $all_fill)) {
                        DB::commit();
                        return response()->json([
                            'errors' => false,
                            'docEntry' => $doc_num,
                            'message' => $id != 'null' ? 'Data updated!' : 'Data inserted!',
                        ]);
                    }
                    DB::commit();
                    return response()->json([
                        'errors' => true,
                        'message' => 'Failed process B/L data!',
                    ], 422);
                }
            }
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'errors' => true,
                'message' => $e->getMessage(),
            ], 422);
        }
    }

    /**
     * @param $id
     * @param $request
     *
     * @return |null
     */
    public function getHeaderDoc($id, $request)
    {
        $header = null;
        if ($id != 'null') {
            $header = HBIMP::where('DocEntry', '=', $id)->first();
        }

        return $header;
    }

    /**
     * @param $jetty
     *
     * @return mixed
     */
    public function getJettyByName($jetty)
    {
        if ($jetty) {
            $data_jetty = DB::table('M_Jetty')->where('Name', '=', $jetty)->first();
            return $data_jetty->DocEntry;
        }
    }

    /**
     * @param $i
     * @param $items
     * @param $request
     * @param $id
     *
     * @return mixed
     */
    public function saveData($i, $items, $request, $id)
    {
    }

    /**
     * @param Request $request
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function exportDetails(Request $request)
    {
        return $this->billingDetails($request, 'Import');
    }

    /**
     * @param Request $request
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function storeExportDetails(Request $request)
    {
        DB::beginTransaction();
        try {
            $details = collect($request->details);
            if ($details) {
                $total_qty = 0;
                foreach ($details as $detail) {
                    if (empty($detail['LoadingQty'])) {
                        return response()->json([
                            'errors' => true,
                            'message' => 'Weight cannot Empty!',
                        ], 422);
                    }

                    $total_qty += $detail['LoadingQty'];
                }
                //check if total input > base qty
                if ($total_qty != $request->dataExport['LoadingQty']) {
                    return response()->json([
                        'errors' => true,
                        'message' => 'Total Qty not equal to base Qty!',
                    ], 422);
                }
                foreach ($details as $detail) {
                    //if ($request->dataExport["LoadingQty"])
                    if (empty($detail['Tenant'])) {
                        return response()->json([
                            'errors' => true,
                            'message' => 'Tenant cannot Empty!',
                        ], 422);
                    }

                    $header = BEXP1::where('DocEntry', '=', $detail['DocEntry'])
                        ->first();
                    $port_service = array_key_exists('PortServiceType', $detail) ?
                        substr($detail['PortServiceType'], 0, 7) :
                        substr($request->form['PortService'], 0, 7);

                    $check_ps = $this->checkPortSeviceExist(
                        $port_service,
                        $request->form['VesselArrival'],
                        $detail['Tenant']
                    );

                    $check_sl = $this->checkServiceLoadingExist(
                        $request->form['VesselArrival'],
                        $detail['Tenant']
                    );

                    if ($check_ps == 'tenant') {
                        return response()->json([
                            'errors' => true,
                            'message' => "Please Specify Price List For ${port_service} AND Tenant " . $detail['Tenant'],
                        ], 422);
                    }
                    if ($check_ps == 'global') {
                        return response()->json([
                            'errors' => true,
                            'message' => "Please Specify Price List For ${port_service}",
                        ], 422);
                    }
                    if ($check_sl == 'tenant') {
                        return response()->json([
                            'errors' => true,
                            'message' => 'Please Specify Price List For Service Loading AND Tenant ' . $detail['Tenant'],
                        ], 422);
                    }
                    if ($check_sl == 'global') {
                        return response()->json([
                            'errors' => true,
                            'message' => 'Please Specify Price List For Service Loading',
                        ], 422);
                    }
                    $price = $this->getPrice(
                        $port_service,
                        $request->form['VesselArrival'],
                        $detail['Tenant'],
                        'Import'
                    );
                    $doc_num = $this->storeBillingDetail(
                        $request,
                        'Import',
                        $price,
                        $header,
                        $detail,
                        $port_service
                    );
                }
                if ($doc_num) {
                    DB::commit();
                    return response()->json([
                        'errors' => false,
                        'message' => 'Data saved!',
                        'total' => DB::table('BEXP1')->where('ExportId', '=', $request->exportId)->count(),
                    ]);
                }
            }
        } catch (\Exception $exception) {
            DB::rollBack();
            return response()->json([
                'errors' => true,
                'trace' => $exception->getTrace(),
                'message' => $exception->getMessage(),
            ], 422);
        }
    }

    public function removeExportDetails(Request $request)
    {
    }

    /**
     * @param $request
     * @param $doc_num
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function reportBilling(Request $request)
    {
        $response = $this->dataReportBilling($request, 'Import');
        // return redirect($res);
        return response()->download($response);
    }

    /**
     * @return \Illuminate\Http\JsonResponse
     */
    public function loadingUnLoading()
    {
        $array_data = [
            'Yes',
            'No',
        ];

        return response()->json([
            'rows' => $array_data,
        ]);
    }

    /**
     * @param Request $request
     * @param $doc_entry
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function cancelDoc(Request $request, $doc_entry)
    {
        try {
            $header = HBIMP::where('DocEntry', '=', $doc_entry)
                ->first();

            $header->Status = 'Cancel';
            $header->UpdatedBy = $request->user()->id;
            $header->updated_at = Carbon::now();
            $header->save();

            DB::table("BEXP")
                ->where("DocNum", "=", $doc_entry)
                ->whereIn("Type", ["Import"])
                ->update([
                    "Status" => "Cancel"
                ]);

            return response()->json([
                'errors' => false,
                'docEntry' => $doc_entry,
                'message' => 'Document cancelled',
            ]);
        } catch (\Exception $exception) {
            return response()->json([
                'errors' => true,
                'message' => $exception->getMessage(),
            ], 422);
        }
    }

    /**
     * @param Request $request
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function weightCategory(Request $request)
    {
        $weight_category = [
            'B/L Weight',
            'Draft Survey',
            'Net Weight',
            'Notification',
            'SI',
            'SOF',
            'BA',
            'Cargo Manifest',
        ];
        return response()->json([
            'data' => $weight_category
        ]);
    }

    /**
     * @param Request $request
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getDocAttachment(Request $request)
    {
        $sort_data = isset($request->sort) ? (string) $request->sort : 'T1.type';
        $order = isset($request->order) ? (string) $request->order : 'ASC';
        $transType = isset($request->transType) ? (string) $request->transType : null;
        $doc_entry_row = isset($request->docEntry) ? (int) $request->docEntry : null;
        $status = isset($request->status) ? (string) $request->status : null;

        if ($transType == 'BillingDetail') {
            $doc_entry_row = isset($request->docEntryRow) ? (int) $request->docEntryRow : null;
        }

        $result = [];
        // jkt
        $query_attachment = DB::table('M_Doc_attachment AS T1')
            ->selectRaw("T1.DocEntry
                ,T1.*
                ,CASE
                    WHEN T1.TransType = 'BillingImport' THEN
                    (
                        SELECT TOP 1 DocNum FROM BHIMP WHERE DocEntry = ${doc_entry_row}
                    )
                    WHEN T1.TransType = 'BillingExport' THEN
                    (
                        SELECT TOP 1 DocNum FROM BHEXP WHERE DocEntry = ${doc_entry_row}
                    )
                    WHEN T1.TransType = 'BillingDetail' THEN
                    (
                        SELECT TOP 1 DocNum FROM BEXP WHERE DocEntry = ${doc_entry_row}
                    )
                    ELSE
                    (
                        SELECT TOP 1 DocNum FROM BHLOCAL WHERE DocEntry = ${doc_entry_row}
                    )
                END AS HeaderNo
                ,T1.SecretKey")
            ->leftJoin('users AS T2', 'T2.username', 'T1.Created_by')
            ->where('T1.M_doc_key', '=', $doc_entry_row);

        $attachment_jkt = clone $query_attachment;
        $attachment_other = clone $query_attachment;

        $data = $attachment_jkt
            ->where('TransType', '=', $transType)
            ->get();

        $data_other = $attachment_other
            ->where('TransType', '=', 'BillingOther')
            ->get();

        $count_all = DB::table('M_Doc_attachment')
            ->where('M_doc_key', '=', $doc_entry_row)
            ->where('TransType', '=', $transType)
            ->count();

        $result['total'] = $data->count();
        $result['totalOther'] = $data_other->count();

        $result = array_merge($result, [
            'rows' => $data,
            'rowOther' => $data_other,
            'count' => $count_all,
        ]);

        return response()->json($result);
    }

    /**
     * @param Request $request
     * @param $docEntry
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function deleteMedia(Request $request, $docEntry)
    {
        try {
            $attach = DB::table('M_Doc_attachment')->where('DocEntry', '=', $docEntry)->first();
            if ($attach->Created_by != Auth::user()->username) {
                return response()->json([
                    'error' => false,
                    'message' => 'Cannot delete file with this user!',
                ]);
            }
            $transType = $request->transType;
            $headerNo = $request->headerNo;
            $attachment = $request->name;
            if (custom_disk_check(custom_disk_check("docs/BILLING/${transType}/${headerNo}/${attachment}"))) {
                custom_disk_delete(custom_disk_check("docs/BILLING/${transType}/${headerNo}/${attachment}"));
                DB::table('M_Doc_attachment')->where('DocEntry', '=', $docEntry)->delete();

                return response()->json([
                    'error' => false,
                    'message' => 'File deleted successfully!',
                ]);
            }
            return response()->json([
                'error' => true,
                'message' => 'File not exist!',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => true,
                'message' => $e->getMessage(),
            ]);
        }
    }

    /**
     * @param Request $request
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function emailDocument(Request $request)
    {
        try {
            $m_doc_key = $request->docEntryRow;
            $trans_type = $request->transType;
            switch ($trans_type) {
                case 'BillingImport':
                    $table_header = 'BHIMP';
                    $foreign_id = 'ImportId';
                    $foreign_table = 'T_MDOC_Header';
                    $email_subject = 'Port Services & Service Loading/Discharge PT. IMIP';
                    $type_doc = 'Import';

                    $header = DB::table('BHIMP AS A')
                        ->leftJoin('T_MDOC_Header AS B', 'B.DocEntry', 'A.ImportId')
                        ->where('A.DocEntry', '=', $m_doc_key)
                        //->where("A.Type", "=", $type_doc)
                        ->selectRaw("
                        A.DocEntry
                        ,CONCAT(B.Cargo, ' VOY. ', B.Voyage) AS VesselName
                        , B.Vessel_arrive                      AS VesselArrival
                        , B.VesselDeparture
                        ")
                        ->first();

                    break;
                case 'BillingExport':
                    $table_header = 'BHEXP';
                    $foreign_id = 'ExportId';
                    $foreign_table = 'THEXP';
                    $type_doc = 'Export';
                    $email_subject = 'Port Services & Service Loading/Discharge PT. IMIP';

                    $header = DB::table("${table_header} AS A")
                        ->leftJoin("${foreign_table} AS B", 'B.DocEntry', "A.${foreign_id}")
                        ->where('B.DocType', '=', 'Export')
                        ->where('A.DocEntry', '=', $m_doc_key)
                        //->where("A.Type", "=", $type_doc)
                        ->selectRaw('
                        A.DocEntry
                        ,(SELECT Name FROM M_CARGO WHERE DocEntry = B.VesselName) AS VesselName
                        , B.VesselArrival
                        , B.VesselDeparture
                        ')
                        ->first();
                    break;
                default:
                    $table_header = 'BHLOCAL';
                    $foreign_id = 'LocalId';
                    $foreign_table = 'L_Master';
                    $email_subject = 'Port Services PT. IMIP';
                    $document = DB::table('BHLOCAL')->where('DocEntry', '=', $m_doc_key)->first();
                    $type_doc = $document->Type;

                    $header = DB::table("${table_header} AS A")
                        ->leftJoin("${foreign_table} AS B", 'B.DocEntry', "A.${foreign_id}")
                        ->where('A.DocEntry', '=', $m_doc_key)
                        ->where('A.Type', '=', $type_doc)
                        ->selectRaw("
                        A.DocEntry
                        ,CASE
                            WHEN B.VesselType = 'MV'  THEN
                                (CONCAT((SELECT Name FROM M_CARGO WHERE DocEntry = B.VesselName), ' VOY. ', B.Voyage))
                            WHEN B.VesselType = 'TB'  THEN
                                (CONCAT((SELECT Name FROM M_CARGO WHERE DocEntry = B.VesselName), ' / ',
                                    (SELECT Name FROM M_CARGO WHERE DocEntry = B.tongkang)))
                            ELSE (SELECT Name FROM M_CARGO WHERE DocEntry = B.VesselName)
                        END AS VesselName
                        , B.VesselArrival
                        , B.VesselDeparture
                        ")
                        ->first();

                    break;
            }

            $details = DB::table('BEXP AS A')
                ->where('DocNum', '=', $header->DocEntry)
                ->where('A.Type', '=', $type_doc)
                ->get();

            $content = [
                'Header' => $header,
                'Details' => $details,
            ];

            // $to = "<EMAIL>";

            $to = [
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>',
                // '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>',
                // "<EMAIL>",
                '<EMAIL>',
                '<EMAIL>',
                // '<EMAIL>',
                //"<EMAIL>",
                // "<EMAIL>",
            ];

            $attachments = DB::table('M_Doc_attachment AS T1')
                ->selectRaw("T1.DocEntry
                ,T1.*
                ,CASE
                    WHEN T1.TransType = 'BillingImport' THEN
                    (
                        SELECT TOP 1 DocNum FROM BHIMP WHERE DocEntry = ${m_doc_key}
                    )
                    WHEN T1.TransType = 'BillingExport' THEN
                    (
                        SELECT TOP 1 DocNum FROM BHEXP WHERE DocEntry = ${m_doc_key}
                    )
                    ELSE
                    (
                        SELECT TOP 1 DocNum FROM BHLOCAL WHERE DocEntry = ${m_doc_key}
                    )
                END AS HeaderNo
                ,T1.SecretKey")
                ->leftJoin('users AS T2', 'T2.username', 'T1.Created_by')
                ->where('TransType', '=', $trans_type)
                ->where('T1.M_doc_key', '=', $m_doc_key)
                ->get();
            $file_attachment = [];
            foreach ($attachments as $attachment) {
                $destination_path = public_path("docs/BILLING/${trans_type}/{$attachment->HeaderNo}");
                $filePath = $destination_path . '/' . $attachment->name;
                create_file_delete_job($filePath);
                $file_attachment[] = $destination_path . '/' . $attachment->name;
            }

            $cc = [];

            BillingJobs::dispatch($email_subject, $content, $to, $cc, $file_attachment);
            return response()->json([
                'error' => false,
                'message' => 'Email send!',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => false,
                'message' => $e->getMessage(),
                'Trace' => $e->getTrace(),
            ]);
        }
    }

    /**
     * @param Request $request
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function storeMedia(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'images.*' => 'image|mimes:jpeg,png,jpg|max:2048',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'errors' => true,
                'message' => 'Silahkan periksa file yang akan di upload,
                pastikan extensi file sesuai dengan yang ditentukan!',
                'messages' => $validator->errors(),
            ]);
        }
        try {
            $m_doc_key = $request->M_doc_key;
            $trans_type = $request->TransType;
            if (!empty($m_doc_key) && $trans_type != 'BillingLocalnull') {
                if ($request->hasFile('file')) {
                    $data_file = $request->file('file');
                    switch ($trans_type) {
                        case 'BillingImport':
                            $table_header = 'BHIMP';
                            break;
                        case 'BillingExport':
                            $table_header = 'BHEXP';
                            break;
                        case 'BillingDetail':
                            $table_header = 'BEXP';
                            break;
                        default:
                            $table_header = 'BHLOCAL';
                            break;
                    }
                    if (
                        DB::table($table_header)
                            ->where('DocEntry', '=', $m_doc_key)
                            ->count() < 1
                    ) {
                        return response()->json([
                            'errors' => true,
                            'message' => 'Document Header Not Saved Yet!',
                        ]);
                    }
                    $extension = $data_file->getClientOriginalExtension();
                    $status = $request->Status;
                    $notif_header = null;
                    $notif_details = null;

                    $bl_data = DB::table("${table_header} AS a")
                        ->where('a.DocEntry', '=', $m_doc_key)
                        ->selectRaw('
                                a.DocNum,
                                a.DocNum AS DocHeader
                            ')
                        ->first();

                    $bl_no = preg_replace("/:?(\s+)?(<?>?\.?\/?)/", '', $bl_data->DocNum);
                    $doc_header = $bl_data->DocHeader;
                    $destination_path = custom_disk_path("docs/BILLING/${trans_type}/${bl_no}");

                    $origin_name = strtoupper(Str::slug($data_file->getClientOriginalName()));
                    $name_no_ext = pathinfo($origin_name, PATHINFO_FILENAME) . '_' . date('ymdhis');
                    $file_name = preg_replace("/:?(\s+)?(<?>?\.?\/?)/", '', $name_no_ext) . '.' . $extension;
                    $data_file->storeAs($destination_path, $file_name, 'sftp');

                    $attach = new DocAttachment();
                    $attach->name = $file_name;
                    $attach->Type = '';
                    $attach->TransType = $trans_type;
                    $attach->DocType = 'Billing';
                    $attach->M_doc_key = $m_doc_key ?? 0;
                    $attach->Created_by = Auth::user()->username;
                    $attach->CreatedName = Auth::user()->name;
                    $attach->save();

                    if ($table_header != 'BEXP') {
                        DB::table("${table_header}")
                            ->where('DocEntry', '=', $m_doc_key)
                            ->update([
                                'Status' => 'Open-upload',
                            ]);
                    }

                    return response()->json([
                        'errors' => false,
                        'message' => 'Document Uploaded!',
                        'type' => $request->TransType,
                    ]);
                }
                return response()->json([
                    'errors' => true,
                    'message' => 'No file Uploaded!',
                ]);
            }
            return response()->json([
                'errors' => true,
                'message' => 'Document Header Not Found!',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'errors' => true,
                'message' => $e->getMessage() . ' - ' . $e->getLine() . ' - ' . $e->getFile(),
            ]);
        }
    }

    /**
     * @return \Illuminate\Http\JsonResponse
     */
    public function exportParams()
    {
        return response()->json([
            'data' => [
                [
                    'value' => 'departure_agreement',
                    'title' => 'Surat Persetujuan Keberangkatan',
                ],
                [
                    'value' => 'berthing_agreement',
                    'title' => 'Surat Persetujuan Sandar',
                ],
                [
                    'value' => 'port_service_service_loading',
                    'title' => 'Port Service And Service Loading',
                ],
                [
                    'value' => 'time_sheet_berita_acara',
                    'title' => 'Time Sheet And Berita Acara',
                ],
            ]
        ]);
    }

    /**
     * @return \Illuminate\Http\JsonResponse
     */
    public function signatureList()
    {
        $signature = Signature::orderBy('name')->pluck('name');
        return response()->json([
            'data' => $signature
        ]);
    }

    /**
     * @param $header
     * @param $created
     * @param $request
     *
     * @return mixed
     */
    protected function processHeaderDoc($header, $created, $request)
    {
        if ($header) {
            $header->PortService = $request->form['PortService'];
            $header->BillingNoteDate = $request->form['BillingNoteDate'] ? date('Y-m-d', strtotime($request->form['BillingNoteDate'])) : null;
            $header->PeriodDate = $request->form['PeriodDate'] ?
                date('Y-m', strtotime($request->form['PeriodDate'])) . '-01' : null;
            //$header->Status = $request->form['Status'];
            $header->Jetty = $this->getJettyByName($request->form['Jetty']);
            $header->updated_at = Carbon::now();
            $header->Remarks = $request->form['Remarks'];
            $header->created_at = $created;
            $header->UpdatedBy = $request->user()->id;
            $header->save();
            $doc_num = $header->DocEntry;
        } else {
            $header = new HBIMP();
            $header->DocNum = $this->generateDocNum(Carbon::now());
            $header->PostingDate = date('Y-m-d');
            $header->BillingNoteDate = $request->form['BillingNoteDate'] ? date('Y-m-d', strtotime($request->form['BillingNoteDate'])) : null;
            $header->PeriodDate = $request->form['PeriodDate'] ?
                date('Y-m', strtotime($request->form['PeriodDate'])) . '-01' : null;
            $header->PortService = $request->form['PortService'];
            $header->Status = 'Open';
            $header->YearArrival = date('Y');
            $header->Jetty = $this->getJettyByName($request->form['Jetty']);
            $header->ImportID = $request->form['ExportId'];
            $header->Remarks = array_key_exists('Remarks', $request->form) ? $request->form['Remarks'] : '';
            $header->created_at = Carbon::now();
            $header->CreatedBy = $request->user()->id;
            $header->UpdatedBy = 0;
            $header->save();
            $doc_num = $header->DocEntry;
        }
        //        DB::table("T_MDOC_Header")
//            ->where("DocEntry", "=", $request->form['ExportId'])
//            ->update([
//                "VesselDeparture" => $request->form['VesselDeparture']
//            ]);
        return $doc_num;
    }
}
