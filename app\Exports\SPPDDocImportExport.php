<?php

namespace App\Exports;

use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Events\AfterSheet;
use Maatwebsite\Excel\Events\BeforeSheet;
use Maatwebsite\Excel\Sheet;

class SPPDDocImportExport implements FromView, ShouldAutoSize, WithEvents
{
    /**
     * @return array
     */
    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event) {
                $cellRange = 'A1:W1'; // All headers
                $event->sheet->getDelegate()->getStyle($cellRange)->getFont()->setSize(12);
            },
            BeforeSheet::class => function (BeforeSheet $event) {
                Sheet::macro('styleCells', function (Sheet $sheet, string $cellRange, array $style) {
                    $sheet->getDelegate()->getStyle($cellRange)->applyFromArray($style);
                });
            }
        ];
    }

    /**
     * @param $bc_key
     * @return mixed
     */
    public function bcType($bc_key)
    {
        $bctype = DB::table("M_TBC")->where("DocEntry", "=", $bc_key)->first();
        return $bctype->Type;
    }

    /**
     * @return View
     */
    public function view(): View
    {

        $tenant = session('tenant');
        $type = session('type');

        $firstDateType = session('firstDateType');
        $dateType = session('dateType');
        $secondDateType = session('secondDateType');
        $firstNumType = session('firstNumType');
        $secondNumType = session('secondNumType');

        if ($type != '2.0' && $type != '2.3') {
            $query = DB::table("R_Master AS T3")
            ->leftJoin("T_MDOC AS T0", "T3.DocEntry", "T0.DocNum")
            ->leftJoin("M_TBC AS T1", "T1.DocEntry", "T0.BC_type_key")
            ->leftJoin("M_Tenant AS T2", "T2.DocEntry", "T0.Tenant_key")
            ->selectRaw("
                T3.DocNo AS DocNum
                ,T0.DocEntry
                ,T3.DocEntry AS DocNo
                ,T2.Name AS Tenant
                ,T0.contract_no AS No_bl
                ,T0.AJU_No
                ,CONVERT(VARCHAR, T0.Date_bl, 105) AS Date_bl
                ,T0.REG_No
                ,CONVERT(VARCHAR, T0.REG_date, 105) AS REG_date
                ,T0.SPPB_No
                ,CONVERT(VARCHAR, T0.SPPB_date, 105) AS SPPB_date
                ,T0.SPPD_No
                ,CONVERT(VARCHAR, T0.SPPD_date, 105) AS SPPD_date
                ,CONVERT(VARCHAR, T0.SPPB_Date_update, 113) AS SPPB_Date_update
                ,CONVERT(VARCHAR, T0.SPPD_Date_update, 113) AS SPPD_Date_update
                ,(
                    SELECT TOP 1 ProcessDate FROM L_ProcessDoc
                    WHERE DocNum = T0.DocEntry AND Status='Process by Tenant'
                    order by ProcessDate DESC
                ) AS ProcessToTenant
                ,(
                    SELECT TOP 1 ProcessDate FROM L_ProcessDoc
                    WHERE DocNum = T0.DocEntry AND Status='Process by PPJK'
                    order by ProcessDate DESC
                ) AS ProcessToPPJK
                ,(
                    SELECT COUNT(*) FROM M_Doc_attachment
                    WHERE M_doc_key = T0.DocEntry
                    AND name LIKE '%SPPD%'
                    AND Created_by IN ( SELECT username FROM users WHERE permission_id = 3  )
                ) AS CountAttachment
            ")
            ->when($dateType, function ($query) use ($dateType, $firstDateType, $secondDateType) {
                if ($dateType == 'SPPD') {
                    return $query->whereRaw("T0.SPPD_date BETWEEN '$firstDateType' AND '$secondDateType' ")
                        ->whereRaw("T0.SPPD_No IS NOT NULL AND T0.SPPD_date IS NOT NULL ");
                } elseif ($dateType == 'SPPD_update') {
                    return $query->whereRaw("CONVERT(VARCHAR, T0.SPPD_Date_update, 23)
                    BETWEEN '$firstDateType' AND '$secondDateType' ")
                        ->whereRaw("T0.SPPD_No IS NOT NULL AND T0.SPPD_date IS NOT NULL ");
                } elseif ($dateType == 'BL') {
                    return $query->whereRaw("T0.Date_bl BETWEEN '$firstDateType' AND '$secondDateType' ")
                        ->whereRaw("T0.SPPD_No IS NOT NULL AND T0.SPPD_date IS NOT NULL ");
                } elseif ($dateType == 'NoPen') {
                    return $query->whereRaw("T0.REG_date BETWEEN '$firstDateType' AND '$secondDateType' ")
                        ->whereRaw("T0.SPPD_No IS NOT NULL AND T0.SPPD_date IS NOT NULL ");
                }
            })
            // ->orderBY("T0.BP", "ASC")
            ->whereRaw("T3.DocNo IS NOT NULL AND T1.Type = '$type'")
            ->orderBY(DB::raw("CONVERT(VARCHAR, T0.SPPD_Date_update, 20)"), "DESC")
            ->get();
        } else {
            $query = DB::table("T_MDOC_Header AS T3")
            ->leftJoin("T_MDOC AS T0", "T3.DocEntry", "T0.DocNum")
            ->leftJoin("M_TBC AS T1", "T1.DocEntry", "T0.BC_type_key")
            ->leftJoin("M_Tenant AS T2", "T2.DocEntry", "T0.Tenant_key")
            ->selectRaw("
                T3.DocNum
                ,T0.DocEntry
                ,T3.DocEntry AS DocNo
                ,T2.Name AS Tenant
                ,T0.No_bl
                ,T0.AJU_No
                ,CONVERT(VARCHAR, T0.Date_bl, 105) AS Date_bl
                ,T0.REG_No
                ,CONVERT(VARCHAR, T0.REG_date, 105) AS REG_date
                ,T0.SPPB_No
                ,CONVERT(VARCHAR, T0.SPPB_date, 105) AS SPPB_date
                ,T0.SPPD_No
                ,CONVERT(VARCHAR, T0.SPPD_date, 105) AS SPPD_date
                ,CONVERT(VARCHAR, T0.SPPB_Date_update, 113) AS SPPB_Date_update
                ,CONVERT(VARCHAR, T0.SPPD_Date_update, 113) AS SPPD_Date_update
                ,(
                    SELECT TOP 1 ProcessDate FROM L_ProcessDoc
                    WHERE DocNum = T0.DocEntry AND Status='Process by Tenant'
                    order by ProcessDate DESC
                ) AS ProcessToTenant
                ,(
                    SELECT TOP 1 ProcessDate FROM L_ProcessDoc
                    WHERE DocNum = T0.DocEntry AND Status='Process by PPJK'
                    order by ProcessDate DESC
                ) AS ProcessToPPJK
                ,(
                    SELECT COUNT(*) FROM M_Doc_attachment
                    WHERE M_doc_key = T0.DocEntry
                    AND name LIKE '%SPPD%'
                    AND Created_by IN ( SELECT username FROM users WHERE permission_id = 3  )
                ) AS CountAttachment
            ")
            ->when($dateType, function ($query) use ($dateType, $firstDateType, $secondDateType) {
                if ($dateType == 'SPPD') {
                    return $query->whereRaw("T0.SPPD_date BETWEEN '$firstDateType' AND '$secondDateType' ")
                        ->whereRaw("T0.SPPD_No IS NOT NULL AND T0.SPPD_date IS NOT NULL ");
                } elseif ($dateType == 'SPPD_update') {
                    return $query->whereRaw("CONVERT(VARCHAR, T0.SPPD_Date_update, 23)
                    BETWEEN '$firstDateType' AND '$secondDateType' ")
                        ->whereRaw("T0.SPPD_No IS NOT NULL AND T0.SPPD_date IS NOT NULL ");
                } elseif ($dateType == 'BL') {
                    return $query->whereRaw("T0.Date_bl BETWEEN '$firstDateType' AND '$secondDateType' ")
                        ->whereRaw("T0.SPPD_No IS NOT NULL AND T0.SPPD_date IS NOT NULL ");
                } elseif ($dateType == 'NoPen') {
                    return $query->whereRaw("T0.REG_date BETWEEN '$firstDateType' AND '$secondDateType' ")
                        ->whereRaw("T0.SPPD_No IS NOT NULL AND T0.SPPD_date IS NOT NULL ");
                }
            })
            // ->orderBY("T0.BP", "ASC")
            ->whereRaw("T3.DocNum IS NOT NULL AND T1.Type = '$type'")
            ->orderBY(DB::raw("CONVERT(VARCHAR, T0.SPPD_Date_update, 20)"), "DESC")
            ->get();
        }

        return view(
            'export.report.sppd',
            [
                'doc' => $query,
                'bc_desc' => $type,
            ]
        );


        // Forget multiple keys...
        session()->forget(
            [
                "tenant",
                "type",
                "dateType",
                "firstDateType",
                "secondDateType",
                "numType",
                "firstNumType",
                "secondNumType"
            ]
        );
    }
}
