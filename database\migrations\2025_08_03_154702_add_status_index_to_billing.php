<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('BHEXP', function (Blueprint $table) {
            $table->index(['Status']);
        });
        Schema::table('BHIMP', function (Blueprint $table) {
            $table->index(['Status']);
        });
        Schema::table('BHLOCAL', function (Blueprint $table) {
            $table->index(['Status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('billing', function (Blueprint $table) {
            //
        });
    }
};
