<?php

namespace App\Exports;

use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Events\AfterSheet;
use Maatwebsite\Excel\Events\BeforeSheet;
use Maatwebsite\Excel\Sheet;

class ServiceLoadingPortServiceExport implements FromView, ShouldAutoSize, WithEvents
{
    private $dataExport;
    private $vesselName;
    private $voyage;
    private $billing_type;
    private $report_type;

    /**
     * ServiceLoadingPortServiceExport constructor.
     * @param $dataExport
     * @param $vesselName
     * @param $voyage
     * @param $billing_type
     * @param $report_type
     */
    public function __construct($dataExport, $vesselName, $voyage, $billing_type, $report_type)
    {
        $this->dataExport = $dataExport;
        $this->vesselName = $vesselName;
        $this->voyage = $voyage;
        $this->billing_type = $billing_type;
        $this->report_type = $report_type;
    }

    /**
     * @return array
     */
    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event) {
                $cellRange = 'A1:W1'; // All headers
                $event->sheet->getDelegate()->getStyle($cellRange)->getFont()->setSize(12);
            },
            BeforeSheet::class => function (BeforeSheet $event) {
                Sheet::macro('styleCells', function (Sheet $sheet, string $cellRange, array $style) {
                    $sheet->getDelegate()->getStyle($cellRange)->applyFromArray($style);
                });
            }
        ];
    }

    public function view(): View
    {
        if ($this->report_type == 'ps') {
            return view('export.report.portService', [
                'doc' => $this->dataExport,
                'vesselName' => $this->vesselName,
                'voyage' => $this->voyage,
                'billing_type' => $this->billing_type,
            ]);
        } else {
            return view('export.report.serviceLoading', [
                'doc' => $this->dataExport,
                'vesselName' => $this->vesselName,
                'voyage' => $this->voyage,
                'billing_type' => $this->billing_type,
            ]);
        }
    }
}
