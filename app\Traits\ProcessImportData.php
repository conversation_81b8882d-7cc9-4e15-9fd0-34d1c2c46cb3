<?php

namespace App\Traits;

use App\Models\Audit\Audit;
use App\Models\Master\BcType;
use App\Models\Tenant\Tenant;
use App\Models\Transaction\MonitorDoc;
use App\Models\Transaction\MonitorDocBl;
use App\Models\Transaction\MonitorDocInv;
use App\Models\Transaction\MonitorDocInvSub;
use App\Models\Transaction\TMDocHeaderBl;
use App\Models\Transaction\TMDocHeaderIinv;
use App\Models\Transaction\TMDocHeaderInvSub;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Modules\BoundedZoneTransaction\App\Models\MonitorDocBZ;
use Modules\BoundedZoneTransaction\App\Models\MonitorDocRef;
use Modules\Master\App\Models\MasterGroup;
use Modules\Master\App\Models\MasterWarehouse;
use PDO;

trait ProcessImportData
{
    use TransformDataImport;

    /**
     * @param $details
     * @param $docNum
     * @param $form
     * @param $id
     * @param $request
     *
     * @return JsonResponse
     * @throws \Exception
     */
    public function loopDetails($details, $docNum, $form, $id, $request)
    {
        $connection = [];
        $connection2 = [];

        $sppbExist = [];
        $sppdExist = [];
        $sppbExistList = [];
        $sppdExistList = [];
        $countSppb = false;
        $countSppd = false;

        if (!$details) {
            return response()->json([
                'errors' => false,
                'docEntry' => $docNum,
                'message' => $id != 'null' ? 'Data updated!' : 'Data inserted!',
            ]);
        }

        $updateDoc = str_replace('.', '', microtime(true));
        $details = $this->transformDetails($details);
        $this->validateDetails($details, $request);

        foreach ($details as $indexBl => $items) {
            $lastData = $items['DocEntry'] ?? null;
            $monitorDoc = MonitorDoc::where('DocEntry', '=', $lastData)->first();

            $countBl = $this->checkBL($items, $request->form['Cargo'], $items['Tenant_name']);
            if ($countBl >= 1) {
                throw new \App\Exceptions\CustomException('B/L ' . $items['No_bl'] . ' already exists in this shipment!', 1);
            }

            try {
                $docEntry = $this->processDetailItem(
                    $items,
                    $request,
                    $form,
                    $docNum,
                    $updateDoc,
                    $indexBl,
                    $connection,
                    $connection2
                );

                $this->updateStatusIfConditionsMet($items, $docEntry, $id);
                $this->createProcessLogIfNotExists($docEntry);
            } catch (\Exception $e) {
                throw new \App\Exceptions\CustomException($e->getMessage(), 1);
            }
        }

        $this->validateAttachments($sppbExist, $sppbExistList, $sppdExist, $sppdExistList);

        return response()->json([
            'errors' => false,
            'docEntry' => $docNum,
            'message' => $id != 'null' ? 'Data updated!' : 'Data inserted!',
        ]);
    }

    private function processDetailItem($items, $request, $form, $docNum, $updateDoc, $indexBl, &$connection, &$connection2)
    {
        $genSecretKey = $this->generateSecretKey($items);

        $this->checkAttachments($items, $sppbExist, $sppbExistList, $sppdExist, $sppdExistList);

        $this->sendEmailNotifications($items, $form);

        $docEntry = $this->saveData(
            $indexBl,
            $items,
            $request,
            $form,
            $genSecretKey,
            $docNum,
            $updateDoc
        );

        if ($docEntry) {
            $this->moveAssociatedFiles($items, $form, $docEntry);
            $this->processInvoices($docEntry, $genSecretKey, $items, $request, $connection, $connection2);
        }

        return $docEntry;
    }

    private function generateSecretKey($items)
    {
        if (empty($items['SecretKey'])) {
            return str_replace('.', '', microtime(true));
        }

        $countSecretKey = DB::table('T_MDOC')
            ->where('SecretKey', '=', $items['SecretKey'])
            ->count();

        if ($countSecretKey > 1) {
            return str_replace('.', '', microtime(true));
        }

        return $items['SecretKey'];
    }

    private function checkAttachments($items, &$sppbExist, &$sppbExistList, &$sppdExist, &$sppdExistList)
    {
        if (!empty($items['SPPB_No']) || !empty($items['SPPB_date'])) {
            $countAttachment = $this->checkCountAttachmentByPPJK($items['DocEntry'], 'sppb');
            if ($countAttachment < 1 && $items['Type'] == '2.3') {
                $sppbExist[] = 1;
                $sppbExistList[] = $items['No_bl'];
            }
            $sppbExist[] = 0;

            $items['Type'] == '2.0' ? $countSppb = true : $countSppb = false;
        }

        if (!empty($items['SPPD_No']) || !empty($items['SPPD_date'])) {
            $sppdExist[] = 0;
            $countSppd = true;
        } else {
            $countSppd = false;
        }
    }

    private function sendEmailNotifications($items, $form)
    {
        $this->processSendEmailSPPD($items, $form);
        $this->processSendEmailSPPB($items, $form);
    }

    private function moveAssociatedFiles($items, $form, $docEntry)
    {
        $log = Audit::where('auditable_id', '=', $docEntry)
            ->where('auditable_type', '=', "App\Models\Transaction\MonitorDoc")
            ->orderBy('id', 'DESC')
            ->first();

        if (!$log)
            return;

        $newValue = json_decode($log->new_values);
        $oldValue = json_decode($log->old_values);

        if (isset($newValue->No_bl)) {
            $docHeader = $form['DocNum'];
            if (isset($oldValue->No_bl)) {
                $oldBl = strtoupper(Str::slug($oldValue->No_bl));
                $newBl = strtoupper(Str::slug($newValue->No_bl));

                $oldPath = custom_disk_path("docs/IMPORT/${docHeader}/${oldBl}");
                $newPath = custom_disk_path("docs/IMPORT/${docHeader}/${newBl}");
                if (custom_disk_check($oldPath)) {
                    custom_disk_move($oldPath, $newPath);
                }
            }
        }
    }

    private function processInvoices($docEntry, $genSecretKey, $items, $request, &$connection, &$connection2)
    {
        $this->loopInv($docEntry, $genSecretKey, $items, $request, $connection, $connection2);
    }

    private function updateStatusIfConditionsMet($items, $docEntry, $id)
    {
        if ($id != 'null') {
            if ($this->shouldCloseHeader($items, 'sppd')) {
                $this->closeHeaderStatus($docEntry);
            }

            // if ($this->shouldCloseHeader($items, 'sppb')) {
            //     $this->closeHeaderStatus($docEntry);
            // }
        }
    }

    private function shouldCloseHeader($items, $documentType)
    {
        if ($documentType == 'sppd') {
            return $items['Type'] != '2.0' && !empty($items['SPPD_No']) && !empty($items['SPPD_date']);
        }

        if ($documentType == 'sppb') {
            return $items['Type'] != '2.0' && !empty($items['SPPB_No']) && !empty($items['SPPB_date']);
        }

        return false;
    }

    private function createProcessLogIfNotExists($docEntry)
    {
        $countLog = DB::table('L_ProcessDoc')
            ->where('DocNum', '=', $docEntry)
            ->count();

        if ($countLog < 1) {
            $this->createDocProcessLog(
                $docEntry,
                'Open',
                date('Y-m-d'),
                Auth::user()->id,
                Carbon::now()
            );
        }
    }

    private function validateAttachments(&$sppbExist, $sppbExistList, &$sppdExist, $sppdExistList)
    {
        if (in_array(1, $sppbExist)) {
            $listBl = implode(', ', $sppbExistList);
            throw new \App\Exceptions\CustomException("B/L ${listBl} need 2 attachments, but 1 provided. Please fix!", 1);
        }

        if (in_array(1, $sppdExist)) {
            $listBl = implode(', ', $sppdExistList);
            throw new \App\Exceptions\CustomException("B/L ${listBl} does not have SPPD attachments. Please fix!", 1);
        }
    }

    private function throwBusinessException($message)
    {
        throw new \App\Exceptions\CustomException($message, 1);
    }

    /**
     * @return PDO[]
     */
    protected function connectionHana()
    {
        $driver = 'HDBODBC';
        $servername = '10.1.1.115:30015';
        $username = 'SYSTEM';
        $password = 'hanaSmi*8Ultra';
        $connection = new PDO(
            "odbc:Driver=$driver;ServerNode=$servername; Uid=$username;Pwd=$password;CHAR_AS_UTF8=true;"
        );

        $servername2 = '10.1.1.114:30015';
        $username2 = 'SYSTEM';
        $password2 = 'hanaTsi*8Ultra';
        $connection2 = new PDO(
            "odbc:Driver=$driver;ServerNode=$servername2;Uid=$username2;Pwd=$password2;CHAR_AS_UTF8=true;"
        );

        return [
            'connection' => $connection,
            'connection2' => $connection2,
        ];
    }

    /**
     * @throws \Exception
     */
    public function validateDetails($convertArray, $request)
    {
        foreach ($convertArray as $index_bl => $items) {
            $count_bl = $this->checkBL($items, $request->form['Cargo'], $items['Tenant_name']);
            if ($count_bl < 1) {
                if (!empty($items['UnitWeight'])) {
                    if (!Str::contains($items['UnitWeight'], ['MT', 'DMT', 'LMT', 'WMT', 'MTS', 'TNE'])) {
                        if (!Str::contains($items['UnitWeight'], ['KGS', 'KG'])) {
                            throw new \App\Exceptions\CustomException('Row ' . ($index_bl + 1) . ': Gross Weight Unit must be WMT/MT/KGS.', 1);
                        }
                    }
                    if (!Str::contains($items['UnitWeight'], ['KGS', 'KG'])) {
                        if (!Str::contains($items['UnitWeight'], ['MT', 'DMT', 'LMT', 'WMT', 'MTS', 'TNE'])) {
                            throw new \App\Exceptions\CustomException('Row ' . ($index_bl + 1) . ': Gross Weight Unit must be WMT/MT/KGS..', 1);
                        }
                    }
                }

                if (!empty($items['SPPB_No']) || !empty($items['SPPB_date'])) {
                    $count_attahment = $this->checkCountAttachmentByPPJK($items['DocEntry'], 'sppb');
                    if ($count_attahment < 1 && $items['Type'] == '2.3') {
                        $sppb_exist[] = 1;
                        $sppb_exist_list[] = $items['No_bl'];
                        throw new \App\Exceptions\CustomException("Please upload the SPPB attachments for B/L {$items['No_bl']} first!", 1);
                    }
                    $sppb_exist[] = 0;

                    if ($items['Type'] == '2.0') {
                        $count_sppb = true;
                    }
                } else {
                    $count_sppb = false;
                }

                if (!empty($items['SPPD_No']) || !empty($items['SPPD_date'])) {
                    // updated 26-06-2020 from 41 to 42
                    // $count_attahment = $this->checkCountAttachmentByPPJK($items['DocEntry'], 'sppd');
                    // if ($count_attahment < 1) {
                    //     $sppd_exist[] = 1;
                    //     $sppd_exist_list[] = $items['No_bl'];
                    //     return response()->json([
                    //         'errors' => true,
                    //         'message' => "Please upload the SPPD attachment for B/L {$items['No_bl']} first!",
                    //     ], 422);
                    // }
                }

                if (empty($items['No_bl'])) {
                    throw new \App\Exceptions\CustomException('Row ' . ($index_bl + 1) . ': B/L cannot empty!', 1);
                }
                if (empty($items['Date_bl'])) {
                    throw new \App\Exceptions\CustomException('Row ' . ($index_bl + 1) . ': B/L Date cannot empty!', 1);
                }
                if (empty($items['BP'])) {
                    throw new \App\Exceptions\CustomException('Row ' . ($index_bl + 1) . ': Shipper cannot empty!', 1);
                }
                if (empty($items['Tenant_name'])) {
                    throw new \App\Exceptions\CustomException('Row ' . ($index_bl + 1) . ': Tenant cannot empty!', 1);
                }
            }
        }
    }

    protected function getWarehouse($items, $tenantGroupName)
    {
        $warehouseName = $items["WarehouseName"];
        ;
        return MasterWarehouse::whereHas("groupTenant", function ($query) use ($tenantGroupName) {
            $query->where("GroupName", $tenantGroupName);
        })
            ->where("WhsName", $warehouseName)
            ->first();
    }

    /**
     * @param $i
     * @param $items
     * @param $request
     * @param $form
     * @param $generateSecretKey
     *
     * @param null $id
     * @param $updateDoc
     *
     * @return mixed
     */
    public function saveData($i, $items, $request, $form, $generateSecretKey, $id = null, $updateDoc)
    {
        $lastData = $items['DocEntry'] ?? null;
        $monitorDoc = $this->getExistingMonitorDoc($lastData);
        $created = $monitorDoc ? $monitorDoc->created_at : Carbon::now();

        try {
            if ($monitorDoc) {
                $this->updateMonitorDoc($monitorDoc, $items, $form, $request, $updateDoc);
                $this->processBillingData($monitorDoc, $items);
            } else {
                $monitorDoc = $this->createNewMonitorDoc($items, $form, $request, $id, $created, $generateSecretKey, $updateDoc);
            }

            $this->storeMonitorDocBz($items, $monitorDoc);
            $this->storeMonitorDocRef($items, $monitorDoc);

            $this->saveSAPKB($monitorDoc->DocEntry, $monitorDoc, $request, $monitorDoc->exists ? 'update' : 'insert');
            return $monitorDoc->DocEntry;
        } catch (\Exception $e) {
            throw new \App\Exceptions\CustomException("Failed to save data: {$e->getMessage()}", 1);
        }
    }

    private function getExistingMonitorDoc($docEntry)
    {
        return $docEntry ? MonitorDoc::where('DocEntry', '=', $docEntry)->first() : null;
    }

    private function updateMonitorDoc($monitorDoc, $items, $form, $request, $updateDoc)
    {
        $this->setCommonMonitorDocFields($monitorDoc, $items, $form, $updateDoc);
        $monitorDoc->updated_at = Carbon::now();
        $monitorDoc->Updated_by = Auth::user()->username;
        $monitorDoc->Updated_id = Auth::user()->id;
        $monitorDoc->save();
    }

    private function createNewMonitorDoc($items, $form, $request, $id, $created, $generateSecretKey, $updateDoc)
    {
        $monitorDoc = new MonitorDoc();
        $this->setCommonMonitorDocFields($monitorDoc, $items, $form, $updateDoc);
        $monitorDoc->created_at = $created;
        $monitorDoc->Created_by = Auth::user()->username;
        $monitorDoc->Created_id = Auth::user()->id;
        $monitorDoc->SecretKey = $generateSecretKey;
        $monitorDoc->DocNum = $id;
        $monitorDoc->Status = 'Open';
        $monitorDoc->DocType = 'Import';
        $monitorDoc->save();
        return $monitorDoc;
    }

    private function storeMonitorDocRef(array $items, $monitorDoc)
    {
        if (array_key_exists('TemporaryExportId', $items)) {
            $temporaryExportIds = explode(',', $items['TemporaryExportId']);
            MonitorDocRef::where("BaseId", $monitorDoc->DocEntry)
                ->where("DocType", "TemporaryImport")
                ->delete();
            foreach ($temporaryExportIds as $temporaryExportId) {
                if (!empty($temporaryExportId)) {
                    MonitorDocRef::updateOrCreate(
                        [
                            "BaseId" => $monitorDoc->DocEntry,
                            "RefId" => $temporaryExportId,
                            "DocType" => "TemporaryImport"
                        ],
                        [
                            "BaseId" => $monitorDoc->DocEntry,
                            "RefId" => $temporaryExportId,
                            "DocType" => "TemporaryImport"
                        ]
                    );
                }
            }
        }
    }

    private function storeMonitorDocBz(array $items, $monitorDoc)
    {
        if (array_key_exists('Bc16', $items)) {
            $bcType = BcType::where("Type", $items["Type"])
                ->where("Trans_name", "=", "Pembelian")
                ->first();

            if ($bcType) {
                MonitorDocBZ::where([
                    "BaseId" => $monitorDoc->DocEntry,
                ])->delete();

                if ($items["Bc16"] == 'Y') {
                    MonitorDocBZ::updateOrCreate(
                        [
                            "BaseId" => $monitorDoc->DocEntry,
                            "BcTypeId" => $bcType->DocEntry
                        ],
                        [
                            "AJU_No" => (array_key_exists("AJU_No_16", $items)) ? $items["AJU_No_16"] : null,
                            "REG_No" => (array_key_exists("PIB_No_16", $items)) ? $items["PIB_No_16"] : null,
                            "REG_date" => (array_key_exists("PIB_date_16", $items)) ? $this->toDate($items["PIB_date_16"]) : null,
                            "SPPB_No" => (array_key_exists("SPPB_No_16", $items)) ? $items["SPPB_No_16"] : null,
                            "SPPB_date" => (array_key_exists("SPPB_date_16", $items)) ? $this->toDate($items["SPPB_date_16"]) : null,
                            "SPPD_No" => (array_key_exists("SPPD_No_16", $items)) ? $items["SPPD_No_16"] : null,
                            "SPPD_date" => (array_key_exists("SPPD_date_16", $items)) ? $this->toDate($items["SPPD_date_16"]) : null,
                            "IsSelected" => (array_key_exists("Bc16", $items)) ? ((empty($items["Bc16"])) ? "N" : $items["Bc16"]) : "N",
                        ]
                    );
                } else {
                    MonitorDocBZ::updateOrCreate(
                        [
                            "BaseId" => $monitorDoc->DocEntry,
                            "BcTypeId" => $bcType->DocEntry
                        ],
                        [
                            "IsSelected" => (array_key_exists("Bc16", $items)) ? ((empty($items["Bc16"])) ? "N" : $items["Bc16"]) : "N",
                        ]
                    );
                }
            }
        }
    }

    /**
     *
     * @param mixed $monitorDoc
     * @param mixed $items
     * @param mixed $form
     * @param mixed $updateDoc
     * @return void
     */
    private function setCommonMonitorDocFields($monitorDoc, $items, $form, $updateDoc)
    {
        $monitorDoc->Cargo = $form['Cargo'];
        $monitorDoc->CargoNum = $form['Cargo'] ? $this->getDocEntryCargo($form['Cargo']) : null;
        $monitorDoc->BPNum = $items['BP'] ? $this->getDocEntryBP($items['BP']) : null;
        $monitorDoc->Shipement = $form['Shipement'];
        $monitorDoc->Shipement_no = $form['Shipement_no'];
        $monitorDoc->Vessel_arrive = $this->toDate($form['Vessel_arrive']);
        $monitorDoc->Tenant_key = $this->getTenantByName($items['Tenant_name']);
        $monitorDoc->BP = $items['BP'];
        $monitorDoc->PortOfLoading = $items['PortOfLoading'];
        $monitorDoc->IsUrgent = $items['IsUrgent'] ?? 'N';
        $monitorDoc->No_bl = $items['No_bl'];
        $monitorDoc->Date_bl = $this->toDate($items['Date_bl']);
        $monitorDoc->Expired_date = $this->toDate($items['Expired_date']);
        $monitorDoc->Ebilling_date = $this->toDate($items['Ebilling_date']);
        $monitorDoc->EmailToBcDate = $this->toDate($items['EmailToBcDate']);
        $monitorDoc->Skep = $items['InsuranceCurrency'];
        $monitorDoc->Skep_date = $this->toDate($items['InsuranceValue']);
        $monitorDoc->Ocean_freight = $items['Ocean_freight'] ?? null;
        $monitorDoc->Currency = $items['Currency'];
        $monitorDoc->Ocean = $items['Ocean'] ?? null;
        $monitorDoc->ContainerNo = $items['ContainerNo'] ?? null;
        $monitorDoc->Freight_value = $items['Freight_value'];
        $monitorDoc->isFeOri = $items['isFeOri'] ?? 'N';
        $monitorDoc->isFeSend = 'N';
        $monitorDoc->CBM = $items['CBM'];
        $monitorDoc->BC_type_key = $this->getBcType($items['Type'], $monitorDoc, $monitorDoc->exists);
        $monitorDoc->AJU_No = $items['AJU_No'];
        $monitorDoc->PIB_no = $items['PIB_No'];
        $monitorDoc->PIB_date = $items['PIB_date'] ? $this->toDate($items['PIB_date']) : null;
        $monitorDoc->SPPB_No = $items['SPPB_No'];
        $monitorDoc->SPPB_date = $this->toDate($items['SPPB_date']);
        $monitorDoc->SPPD_No = $items['SPPD_No'];
        $monitorDoc->SPPD_date = $this->toDate($items['SPPD_date']);
        $monitorDoc->Remarks = $items['Remarks'];
        $monitorDoc->Color = $items['Remarks'] ? $this->getRemarkColor($items['Remarks']) : null;
        $monitorDoc->isSend = $items['isSend'] ?? 'N';
        $monitorDoc->isOriginal = $items['isOriginal'] ?? 'N';
        $monitorDoc->ItemName = $items['ItemName'];
        $monitorDoc->ItemQty = $items['ItemQty'];
        $monitorDoc->UnitQty = $items['UnitQty'];
        $monitorDoc->GrossWeight = $items['GrossWeight'];
        $monitorDoc->UnitWeight = $items['UnitWeight'];
        $monitorDoc->LetterNo = $items['LetterNo'];
        $monitorDoc->PPJK = $items['PPJKCode'] ? $this->getPPJK($items['PPJKCode']) : null;
        $monitorDoc->REG_No = $items['PIB_No'];
        $monitorDoc->REG_date = $items['PIB_date'] ? $this->toDate($items['PIB_date']) : null;
        $monitorDoc->IsParent = $items['IsParent'] ?? 'N';
        $monitorDoc->PPJKCodeTemp = $items['PPJKCode'];
        $monitorDoc->isChange = $items['isChange'] ?? '';
        $monitorDoc->SPPBStatus = $items['SPPBStatus'] ?? '';
        $monitorDoc->Flags = $monitorDoc->Flags ?? $this->getFlags();
        $monitorDoc->UpdateDate = $updateDoc;
        $monitorDoc->InsuranceCurrency = $items['InsuranceCurrency'];
        $monitorDoc->InsuranceValue = $items['InsuranceValue'];
        $monitorDoc->Rate = $items['Rate'];
        $monitorDoc->BillingDate = $items['BillingDate'] ? $this->toDate($items['BillingDate']) : null;
    }

    private function processBillingData($monitorDoc, $items)
    {
        $billingCount = DB::table('BEXP')
            ->where('BaseId', '=', $monitorDoc->DocEntry)
            ->count();

        if ($billingCount > 0) {
            $bp = $items['BP'] ? $this->getDocEntryBP($items['BP']) : 0;
            $tenant = $items['Tenant_name'] ? $this->getTenantByName($items['Tenant_name']) : 0;

            DB::table('BEXP')
                ->where('BaseId', '=', $monitorDoc->DocEntry)
                ->update([
                    'BP' => $bp,
                ]);

            $checkNotClosed = DB::table('BEXP')
                ->where('BaseId', '=', $monitorDoc->DocEntry)
                ->first();

            if ($checkNotClosed->Status != 'Closed') {
                DB::table('BEXP')
                    ->where('BaseId', '=', $monitorDoc->DocEntry)
                    ->update([
                        'LoadingItem' => $monitorDoc->ItemName,
                        'TenantId' => $tenant,
                    ]);
            }
        }
    }

    // private function storeMonitorDocBoundedZone($monitor_doc, $items)
    // {
    //     MonitorDocBZ::updateOrCreate([

    //     ]);
    // }

    /**
     * @param $docEntry
     * @param $monitorDoc
     * @param $request
     * @param $status
     */
    public function saveSAPKB($docEntry, $monitorDoc, $request, $status)
    {
        $document = MonitorDoc::where('DocEntry', '=', $docEntry)->first();
        if (!empty($monitorDoc->PIB_no) && !empty($monitorDoc->PIB_date)) {
            $document->REG_No = $monitorDoc->PIB_no;
            $document->REG_date = $monitorDoc->PIB_date;
            $document->save();

            if (!empty($monitorDoc->BC_type_key) || $monitorDoc->BC_type_key != 0) {
                $bc_type = $this->getBc($monitorDoc->BC_type_key);

                $tenant = DB::table('M_Tenant')->where('DocEntry', '=', $monitorDoc->Tenant_key)->first();
                $arr = [
                    'REG_No' => $document->REG_No,
                    'REG_date' => $document->REG_date,
                    'BP' => $document->BP,
                    'Trans_no' => $bc_type->Trans_no,
                    'Trans_name' => $bc_type->Trans_name,
                ];
                $request_all = [
                    'Trans_no' => $bc_type->Trans_no,
                    'Trans_name' => $bc_type->Trans_name,
                    'BC_type_key' => $monitorDoc->BC_type_key,
                    'Tenant_key' => $monitorDoc->Tenant_key,
                    'BP' => $monitorDoc->BP,
                    'Cargo' => $monitorDoc->Cargo,
                    'Weight' => $monitorDoc->Weight,
                    'BL_No' => $monitorDoc->BL_No,
                    'BL_date' => $monitorDoc->BL_date ? $this->toDate($monitorDoc->BL_date) : $monitorDoc->BL_date,
                    'No_bl' => $monitorDoc->No_bl,
                    'Date_bl' => $monitorDoc->Date_bl ? $this->toDate($monitorDoc->Date_bl) : $monitorDoc->Date_bl,
                    'No_inv' => $monitorDoc->No_inv,
                    'Date_inv' => $monitorDoc->Date_inv ? $this->toDate($monitorDoc->Date_inv) : $monitorDoc->Date_inv,
                    'AJU_No' => $monitorDoc->AJU_No,
                    'REG_No' => $monitorDoc->REG_No,
                    'REG_date' => $monitorDoc->REG_date ? $this->toDate($monitorDoc->REG_date) : $monitorDoc->REG_date,
                    'PIB_no' => $monitorDoc->REG_No,
                    'PIB_date' => $monitorDoc->REG_date ? $this->toDate($monitorDoc->REG_date) : $monitorDoc->REG_date,
                    'SPPB_No' => $monitorDoc->SPPB_No,
                    'SPPB_date' => $monitorDoc->SPPB_date ? $this->toDate($monitorDoc->SPPB_date) : $monitorDoc->SPPB_date,
                    'SPPD_No' => $monitorDoc->SPPD_No,
                    'SPPD_date' => $monitorDoc->SPPD_date ? $this->toDate($monitorDoc->SPPD_date) : $monitorDoc->SPPD_date,
                    'Shipement' => $monitorDoc->Shipement,
                    'Remarks' => $monitorDoc->Remarks,
                ];
                // $requestAll = array_merge($request->all(), $arr);
                if ($status == 'insert') {
                    $this->storeSAPKb($request, $request_all, $document->DocEntry, $bc_type->Type, $tenant->Name);
                } elseif ($status == 'update') {
                    $check_exist = DB::table('T_SAP_KB')->where('T_MDOC_entry', '=', $document->DocEntry)->first();
                    if ($check_exist) {
                        $this->updateSAPKb($request, $request_all, $document->DocEntry, $bc_type->Type, $tenant->Name);
                    } else {
                        $this->storeSAPKb($request, $request_all, $document->DocEntry, $bc_type->Type, $tenant->Name);
                    }
                }
            }
        }
    }

    /**
     * @param $docEntry
     * @param $generateSecretKey
     * @param $items
     * @param $request
     * @param $conn
     * @param $conn2
     *
     * @return JsonResponse
     */
    public function loopInv($docEntry, $generateSecretKey, $items, $request, $conn, $conn2)
    {
        if (!$request->invoices) {
            return response()->json(['message' => 'No invoices to process']);
        }

        foreach ($request->invoices as $invoice_index => $invoices) {
            $inv_head = $invoices['header'];

            if ($items['SecretKey'] !== $inv_head['SecretKey']) {
                continue;
            }

            $test_docentry[] = 'ok';

            try {
                $inv_head_entry = $this->storeInv($inv_head, $docEntry, $generateSecretKey);

                if (!$inv_head_entry) {
                    throw new \App\Exceptions\CustomException('Failed to process invoice header!', 1);
                }

                foreach (collect($invoices['rows']) as $inv_row_index => $inv_row) {
                    $this->processInvoiceRow($request, $inv_row, $inv_head_entry, $docEntry, $inv_row_index, $conn, $conn2);
                }
            } catch (\Exception $e) {
                throw new \App\Exceptions\CustomException($e->getMessage(), 1);
            }
        }

        return response()->json(['message' => 'Success process invoice']);
    }

    private function processInvoiceRow($request, $inv_row, $inv_head_entry, $docEntry, $inv_row_index, &$conn, &$conn2)
    {
        $gen_key_inv = $this->generateInvoiceSecretKey($inv_row);

        $inv_doc_entry = $this->saveDataInvoice(
            $inv_row_index,
            $inv_row,
            $inv_head_entry,
            $docEntry,
            $gen_key_inv
        );

        if (!$inv_doc_entry) {
            throw new \App\Exceptions\CustomException('Failed process Invoice data!', 1);
        }

        if ($gen_key_inv) {
            $this->updateAttachmentMDocKey($inv_row['SecretKey'], $inv_doc_entry, 'Inv');
        }

        $this->loopInvDetails($inv_doc_entry, $inv_row, $request, $docEntry, $conn, $conn2);
    }

    private function generateInvoiceSecretKey($inv_row)
    {
        if (empty($inv_row['SecretKey'])) {
            return str_replace('.', '', microtime(true));
        }

        $count_secret_key = DB::table('T_MDOC_bl')
            ->where('SecretKey', '=', $inv_row['SecretKey'])
            ->count();

        if ($count_secret_key > 1) {
            return str_replace('.', '', microtime(true));
        }

        return $inv_row['SecretKey'];
    }

    private function processInvoiceHeader($inv_head, $docEntry, $generateSecretKey)
    {
        $inv_head_entry = $this->storeInv($inv_head, $docEntry, $generateSecretKey);

        if (!$inv_head_entry) {
            throw new \App\Exceptions\CustomException('Failed to process invoice header!', 1);
        }

        return $inv_head_entry;
    }

    /**
     * @param $items
     * @param $docEntry
     * @param $generateSecretKey
     *
     * @return mixed
     */
    public function storeInv($items, $docEntry, $generateSecretKey)
    {
        $header = TMDocHeaderBl::where('SecretKey', '=', $items['SecretKey'])
            ->where('DocNum', '=', $docEntry)
            ->first();
        $created = !empty($header->created_at) ? $header->created_at : Carbon::now();

        if ($header) {
            $header->No_bl = $items['No_bl'];
            $header->Date_bl = $this->toDate($items['Date_bl']);
            $header->DocNum = $docEntry;
            $header->PIB_no = $items['PIB_no'];
            $header->PIB_date = $this->toDate($items['PIB_date']);
            $header->updated_at = Carbon::now();
            $created_at = $created;
            $header->updated_at = $created_at;
            $header->Updated_by = Auth::user()->username;
            $header->Flags = 'J';
            $header->save();

            return $header->DocEntry;
        }
        $header = new TMDocHeaderBl();
        $header->No_bl = $items['No_bl'];
        $header->Date_bl = $this->toDate($items['Date_bl']);
        $header->DocNum = $docEntry;
        $header->PIB_no = $items['PIB_no'];
        //$header->Shipper  = $items['Shipper'];
        $header->SecretKey = $items['SecretKey'] ? $items['SecretKey'] : $generateSecretKey;
        $header->MatchKey = $items['MatchKey'];
        $header->PIB_date = $this->toDate($items['PIB_date']);
        $header->created_at = Carbon::now();
        $header->Created_by = Auth::user()->username;
        $header->Flags = 'J';
        $header->save();

        return $header->DocEntry;
    }

    /**
     * [saveDataInvoice description]
     *
     * @param $i
     * @param $items
     * @param $id
     *
     * @return int|mixed [type]        [description]
     */
    public function saveDataInvoice($i, $items, $id, $blKey, $generateSecretKey)
    {
        // $last = $items[7];
        $last = $items['DocEntry'] ? $items['DocEntry'] : null;
        $index = $i;
        $index *= 0;
        $monitorDoc = MonitorDocBl::where('DocEntry', '=', $last)->first();
        $created = !empty($monitorDoc->created_at) ? $monitorDoc->created_at : Carbon::now();
        if ($monitorDoc) {
            $monitorDoc->No_inv = $items['No_inv'];
            $monitorDoc->Date_inv = $this->toDate($items['Date_inv']);
            $monitorDoc->Vendor = $items['Vendor'];
            $monitorDoc->Currency = $items['Currency'];
            $monitorDoc->Value = $items['Value'];
            // $monitorDoc->isScan = $items['isScan'] ? $items['isScan'] : 'N';
            $monitorDoc->isOriginal = $items['isOriginal'] ? $items['isOriginal'] : 'N';
            $monitorDoc->isSend = $items['isSend'] ? $items['isSend'] : 'N';
            $monitorDoc->FormE = $items['FormE'] ? $items['FormE'] : 'N';
            $monitorDoc->isFeOri = $items['isFeOri'] ? $items['isFeOri'] : 'N';
            $monitorDoc->isFeSend = $items['isFeSend'] ? $items['isFeSend'] : 'N';
            $monitorDoc->Updated_by = Auth::user()->username;
            $monitorDoc->updated_at = Carbon::now();
            $monitorDoc->created_at = $created;
            $monitorDoc->Flags = 'U';
            $monitorDoc->save();
            return $monitorDoc->DocEntry;
        }
        $monitorDoc = new MonitorDocBl();
        $monitorDoc->created_at = Carbon::now();
        $monitorDoc->No_inv = $items['No_inv'];
        $monitorDoc->Date_inv = $this->toDate($items['Date_inv']);
        $monitorDoc->Vendor = $items['Vendor'];
        $monitorDoc->Currency = $items['Currency'];
        $monitorDoc->Value = $items['Value'];
        // $monitorDoc->isScan = $items['isScan'] ? $items['isScan'] : 'N';
        $monitorDoc->isOriginal = $items['isOriginal'] ? $items['isOriginal'] : 'N';
        $monitorDoc->isSend = $items['isSend'] ? $items['isSend'] : 'N';
        $monitorDoc->FormE = $items['FormE'] ? $items['FormE'] : 'N';
        $monitorDoc->isFeOri = $items['isFeOri'] ? $items['isFeOri'] : 'N';
        $monitorDoc->isFeSend = $items['isFeSend'] ? $items['isFeSend'] : 'N';
        $monitorDoc->SecretKey = $generateSecretKey;
        //$monitorDoc->SecretKey = $items["SecretKey"];
        $monitorDoc->MatchKey = $items['MatchKey'];
        $monitorDoc->Created_by = Auth::user()->username;
        $monitorDoc->DocNum = $id;
        $monitorDoc->BLKey = $blKey;
        $monitorDoc->Flags = 'I';
        $monitorDoc->save();
        return $monitorDoc->DocEntry;
    }

    /**
     * @param $invDocEntry
     * @param $invRow
     * @param $request
     * @param $blKey
     * @param $conn
     * @param $conn2
     *
     * @return bool|JsonResponse
     */
    public function loopInvDetails($invDocEntry, $invRow, $request, $blKey, $conn, $conn2)
    {
        if ($request->invoiceDetails) {
            foreach ($request->invoiceDetails as $inv_details) {
                $inv_det_hed = $inv_details['header'];
                if ($invRow['SecretKey'] == $inv_det_hed['SecretKey']) {
                    $inv_det_rows = collect($inv_details['rows']);
                    // save invoice header
                    // get doc entry from invoice detail header
                    $inv_det_entry = $this->storeInvDetail(
                        $inv_det_hed,
                        $invDocEntry
                    );
                    if ($inv_det_entry) {
                        foreach ($inv_det_rows as $in_idx => $inv_det) {
                            if (empty($inv_det['SecretKey'])) {
                                $gen_secret_key = str_replace('.', '', microtime(true));
                            } else {
                                $count_secret_key = DB::table('T_MDOC_inv')
                                    ->where('SecretKey', '=', $inv_det['SecretKey'])
                                    ->count();

                                if ($count_secret_key > 1) {
                                    $gen_secret_key = str_replace('.', '', microtime(true));
                                } else {
                                    $gen_secret_key = $inv_det['SecretKey'];
                                }
                            }
                            // save invoice details row
                            $inv_det_docentry = $this->saveDataInvDetails(
                                $in_idx,
                                $inv_det,
                                $inv_det_entry,
                                $blKey,
                                $invDocEntry,
                                $conn,
                                $gen_secret_key,
                                $conn2
                            );
                            if ($inv_det_docentry) {
                                // ---> invoice details sub
                                //TODO
                                //Invoice detail sub
                                $this->loopInvDetailSub(
                                    $inv_det,
                                    $inv_det_docentry,
                                    $request,
                                    $blKey,
                                    $invDocEntry,
                                    $conn,
                                    $conn2
                                );
                            } else {
                                throw new \App\Exceptions\CustomException('Failed process Invoice Details data!', 1);
                            }
                        }
                    } else {
                        throw new \App\Exceptions\CustomException('Failed process Invoice Details Header data!', 1);
                    }
                } // check before save invoices details
            } // invoices details
            return response()->json([
                'message' => 'Success process invoice details',
            ]);
        }
    }

    /**
     * @param $items
     * @param $docEntry
     *
     * @return int|mixed
     */
    public function storeInvDetail($items, $docEntry)
    {
        $header = TMDocHeaderIinv::where('SecretKey', '=', $items['SecretKey'])
            ->where('DocNum', '=', $docEntry)
            ->first();

        $created = !empty($header->created_at) ? $header->created_at : Carbon::now();
        if ($header) {
            $header->No_inv = $items['No_inv'];
            //$header->Shipper = $items['Shipper'];
            $header->Date_inv = $this->toDate($items['Date_inv']);
            $header->DocNum = $docEntry;
            $header->updated_at = Carbon::now();
            $header->Updated_by = Auth::user()->username;
            $header->created_at = $created;
            $header->Flags = 'J';
            $header->save();

            return $header->DocEntry;
        }
        $header = new TMDocHeaderIinv();
        $header->No_inv = $items['No_inv'];
        //$header->Shipper = $items['Shipper'];
        $header->SecretKey = $items['SecretKey'];
        $header->MatchKey = $items['MatchKey'];
        $header->Date_inv = $this->toDate($items['Date_inv']);
        $header->DocNum = $docEntry;
        $header->created_at = Carbon::now();
        $header->Created_by = Auth::user()->username;
        $header->Flags = 'J';
        $header->save();

        return $header->DocEntry;
    }

    /**
     * @param $i
     * @param $items
     * @param $id
     * @param $bl_key
     * @param $invDocEntry
     * @param $conn
     *
     * @return int|mixed
     */
    public function saveDataInvDetails($i, $items, $id, $bl_key, $invDocEntry, $conn, $generateSecretKey, $conn2)
    {
        // $last = $items['InsuranceCurrency'];
        $last_data = $items['DocEntry'] ? $items['DocEntry'] : null;
        $index = $i;
        $index *= 0;
        if ($items['ItemCode']) {
            $data_bl = DB::table('T_MDOC As a')
                ->leftJoin('M_Tenant AS b', 'a.Tenant_key', 'b.DocEntry')
                ->select('b.Name')
                ->where('a.DocEntry', '=', $bl_key)
                ->first();

            if ($data_bl->Name == 'IRNC') {
                $data_item = $this->getItemFromConnection('irnc', $items['ItemCode']);
            } elseif ($data_bl->Name == 'ITSS') {
                $data_item = $this->getItemFromConnection('itss', $items['ItemCode']);
            } elseif ($data_bl->Name == 'GCNS') {
                $data_item = $this->getItemFromConnection('gcns', $items['ItemCode']);
            } elseif ($data_bl->Name == 'TSI') {
                $itm_code = $items['ItemCode'];
                $data_item = $this->loadSingleItemByDB($conn2, $itm_code, 'TSI_LIVE');
            } else {
                $data_item = $this->loadSingleItem($conn, $items['ItemCode']);
            }

            if ($data_item) {
                $item_name_sap = $data_item['ItemName'];
                $frgn_name = $data_item['FrgnName'];
            } else {
                $item_name_sap = null;
                $frgn_name = null;
            }
        } else {
            $item_name_sap = null;
            $frgn_name = null;
        }
        $monitor_doc = MonitorDocInv::where('DocEntry', '=', $last_data)->first();
        $created = !empty($monitor_doc->created_at) ? $monitor_doc->created_at : Carbon::now();
        if ($monitor_doc) {
            $monitor_doc->ContractNo = $items['ContractNo'];
            $monitor_doc->CategoryDesc = $items['CategoryDesc'] ? $items['CategoryDesc'] : '';
            $monitor_doc->Category = $items['CategoryDesc'] ?
                $this->getCategoryItemDocEntry($items['CategoryDesc']) : '';
            $monitor_doc->ItemCode = $items['ItemCode'] ? $items['ItemCode'] : '';
            $monitor_doc->ItemNameSAP = $item_name_sap;
            $monitor_doc->FrgnName = $frgn_name;
            $monitor_doc->ItemName = $items['ItemName'];
            $monitor_doc->Specification = $items['Specification'];
            $monitor_doc->Qty = $items['Qty'];
            $monitor_doc->UoM = $items['UoM'];
            $monitor_doc->UnitPrice = $items['UnitPrice'];
            $monitor_doc->LineTotal = $items['LineTotal'];
            $monitor_doc->HsCode = $items['HsCode'];
            $monitor_doc->Created_by = Auth::user()->username;
            $monitor_doc->created_at = Carbon::now();
            $monitor_doc->Updated_by = Auth::user()->username;
            $monitor_doc->updated_at = Carbon::now();
            $monitor_doc->created_at = $created;
            $monitor_doc->Flags = 'U';
            $monitor_doc->save();
            $docEntry = $monitor_doc->DocEntry;
        } else {
            $monitor_doc = new MonitorDocInv();
            $monitor_doc->ContractNo = $items['ContractNo'];
            $monitor_doc->CategoryDesc = $items['CategoryDesc'] ? $items['CategoryDesc'] : '';
            $monitor_doc->Category = $items['CategoryDesc'] ?
                $this->getCategoryItemDocEntry($items['CategoryDesc']) : '';
            $monitor_doc->ItemCode = $items['ItemCode'] ? $items['ItemCode'] : '';
            $monitor_doc->ItemNameSAP = $item_name_sap;
            $monitor_doc->FrgnName = $frgn_name;
            $monitor_doc->ItemName = $items['ItemName'];
            $monitor_doc->Specification = $items['Specification'];
            $monitor_doc->Qty = $items['Qty'];
            $monitor_doc->UoM = $items['UoM'];
            $monitor_doc->UnitPrice = $items['UnitPrice'];
            $monitor_doc->LineTotal = $items['LineTotal'];
            $monitor_doc->HsCode = $items['HsCode'];
            $monitor_doc->MatchKey = $items['MatchKey'];
            $monitor_doc->SecretKey = $generateSecretKey;
            //$monitorDoc->SecretKey = $items["SecretKey"];
            $monitor_doc->Created_by = Auth::user()->username;
            $monitor_doc->created_at = Carbon::now();
            $monitor_doc->DocNum = $id;
            $monitor_doc->BLKey = $bl_key;
            $monitor_doc->InvKey = $invDocEntry;
            $monitor_doc->Flags = 'I';
            $monitor_doc->save();
            $docEntry = $monitor_doc->DocEntry;
        }
        return $docEntry;
    }

    /**
     * @param $invDet
     * @param $invDetDocEntry
     * @param $request
     * @param $blKey
     * @param $invDocEntry
     * @param $conn
     * @param $conn2
     *
     * @return bool|JsonResponse
     */
    public function loopInvDetailSub($invDet, $invDetDocEntry, $request, $blKey, $invDocEntry, $conn, $conn2)
    {
        if ($request->invoiceDetailsSub) {
            $inv_det_bub = $request->invoiceDetailsSub;

            foreach ($inv_det_bub as $inv_sub_value) {
                $sub_head = $inv_sub_value['header'];
                if (
                    $invDet['SecretKey'] == $sub_head['SecretKey'] &&
                    $invDet['ContractNo'] == $sub_head['ContractNo'] &&
                    $invDet['ItemName'] == $sub_head['ItemName'] &&
                    number_format($invDet['Qty'], 3) == number_format($sub_head['Qty'], 3) &&
                    number_format($invDet['LineTotal'], 2) == number_format($sub_head['Total'], 2)
                ) {
                    $inv_det_sub_row = collect(
                        $inv_sub_value['rows']
                    );
                    // dd($invDetailSubRow);
                    // save head inv detail sub
                    $inv_det_subentry = $this->storeInvDetailSub(
                        $sub_head,
                        $invDetDocEntry
                    );

                    if ($inv_det_subentry) {
                        // Check to delete inv details sub row
                        foreach ($inv_det_sub_row as $invdetailsubrowval) {
                            // save row inv detai sub
                            $inv_detsubentry = $this->saveInvDetailSub(
                                $invdetailsubrowval,
                                $inv_det_subentry,
                                $blKey,
                                $invDocEntry,
                                $invDetDocEntry,
                                $conn,
                                $conn2
                            );
                            if (!$inv_detsubentry) {
                                return response()
                                    ->json([
                                        'errors' => true,
                                        'message' => 'Failed process Sub Item data!',
                                    ], 422);
                            }
                        }
                    } else {
                        return response()
                            ->json([
                                'errors' => true,
                                'message' => 'Failed process Sub Item Header data!',
                            ], 422);
                    }
                }
            }
        } else {
            return false;
        }
    }

    /**
     * @param $items
     * @param $docEntry
     *
     * @return mixed
     */
    public function storeInvDetailSub($items, $docEntry)
    {
        $qty = round($items['Qty'], 2);
        $total = round($items['Total'], 2);

        $header = TMDocHeaderInvSub::where('ContractNo', '=', $items['ContractNo'])
            ->where('ItemName', '=', $items['ItemName'])
            ->whereRaw("CAST(Qty AS DECIMAL(20,0))  = ${qty} ")
            ->whereRaw("CAST(Total AS DECIMAL(20,0))  = ${total} ")
            ->where('DocNum', '=', $docEntry)
            ->first();
        $created = !empty($header->created_at) ? $header->created_at : Carbon::now();
        if ($header) {
            $header->ContractNo = $items['ContractNo'];
            $header->ItemName = $items['ItemName'];
            $header->Qty = $items['Qty'];
            $header->Total = $items['Total'];
            $header->DocNum = $docEntry;
            $header->updated_at = Carbon::now();
            $header->Updated_by = Auth::user()->username;
            $header->created_at = $created;
            $header->Flags = 'J';
            $header->save();

            return $header->DocEntry;
        }
        $header = new TMDocHeaderInvSub();
        $header->ContractNo = $items['ContractNo'];
        $header->ItemName = $items['ItemName'];
        $header->Qty = $items['Qty'];
        $header->Total = $items['Total'];
        $header->SecretKey = $items['SecretKey'] ? $items['SecretKey'] : strtotime(Carbon::now());
        $header->MatchKey = $items['MatchKey'] ? $items['MatchKey'] : strtotime(Carbon::now());
        $header->DocNum = $docEntry;
        $header->created_at = Carbon::now();
        $header->Created_by = Auth::user()->username;
        $header->Flags = 'J';
        $header->save();
        return $header->DocEntry;
    }

    /**
     * [saveInvDetailSub description]
     *
     * @param $items
     * @param $id
     * @param $blKey
     * @param $invDocEntry
     * @param $invDetailSubDocEntry
     * @param $conn
     * @param $conn2
     *
     * @return mixed [type]        [description]
     */
    public function saveInvDetailSub($items, $id, $blKey, $invDocEntry, $invDetailSubDocEntry, $conn, $conn2)
    {
        if ($items['ItemCode']) {
            $item = $this->loadSingleItem($conn, $items['ItemCode']);
            $itemNameSAP = $item['ItemName'];
            $frgnName = $item['FrgnName'];
        } else {
            $itemNameSAP = null;
            $frgnName = null;
        }
        $last = $items['DocEntry'] ? $items['DocEntry'] : null;
        $monitorDoc = MonitorDocInvSub::where('DocEntry', '=', $last)->first();
        $created = !empty($monitorDoc->created_at) ? $monitorDoc->created_at : Carbon::now();
        if ($monitorDoc) {
            $monitorDoc->ContractNo = $items['ContractNo'] ? $items['ContractNo'] : '';
            $monitorDoc->CategoryDesc = $items['CategoryDesc'] ? $items['CategoryDesc'] : '';
            $monitorDoc->Category = $items['CategoryDesc'] ?
                $this->getCategoryItemDocEntry($items['CategoryDesc']) : '';
            $monitorDoc->ItemCode = $items['ItemCode'] ? $items['ItemCode'] : '';
            $monitorDoc->ItemNameSAP = $itemNameSAP;
            $monitorDoc->FrgnName = $frgnName;
            $monitorDoc->ItemName = $items['ItemName'] ? $items['ItemName'] : '';
            $monitorDoc->Qty = $items['Qty'] ? $items['Qty'] : 0;
            $monitorDoc->UoM = $items['UoM'] ? $items['UoM'] : '';
            $monitorDoc->UnitPrice = $items['UnitPrice'] ? $items['UnitPrice'] : 0;
            $monitorDoc->LineTotal = $items['LineTotal'] ? $items['LineTotal'] : 0;
            $monitorDoc->HsCode = $items['HsCode'] ? $items['HsCode'] : '';
            $monitorDoc->Created_by = Auth::user()->username;
            $monitorDoc->created_at = Carbon::now();
            $monitorDoc->Updated_by = Auth::user()->username;
            $monitorDoc->updated_at = Carbon::now();
            $monitorDoc->created_at = $created;
            $monitorDoc->Flags = 'U';
            $monitorDoc->save();
            $docEntry = $monitorDoc->DocEntry;
        } else {
            $monitorDoc = new MonitorDocInvSub();
            $monitorDoc->ContractNo = $items['ContractNo'] ? $items['ContractNo'] : '';
            $monitorDoc->CategoryDesc = $items['CategoryDesc'] ? $items['CategoryDesc'] : '';
            $monitorDoc->Category = $items['CategoryDesc'] ?
                $this->getCategoryItemDocEntry($items['CategoryDesc']) : '';
            $monitorDoc->ItemCode = $items['ItemCode'] ? $items['ItemCode'] : '';
            $monitorDoc->ItemNameSAP = $itemNameSAP;
            $monitorDoc->FrgnName = $frgnName;
            $monitorDoc->ItemName = $items['ItemName'] ? $items['ItemName'] : '';
            $monitorDoc->Qty = $items['Qty'] ? $items['Qty'] : 0;
            $monitorDoc->UoM = $items['UoM'] ? $items['UoM'] : '';
            $monitorDoc->UnitPrice = $items['UnitPrice'] ? $items['UnitPrice'] : 0;
            $monitorDoc->LineTotal = $items['LineTotal'] ? $items['LineTotal'] : 0;
            $monitorDoc->HsCode = $items['HsCode'] ? $items['HsCode'] : '';
            $monitorDoc->isScan = 'N';
            $monitorDoc->isOriginal = 'N';
            $monitorDoc->isSend = 'N';
            $monitorDoc->isFeOri = 'N';
            $monitorDoc->isFeSend = 'N';
            $monitorDoc->MatchKey = $items['MatchKey'] ? $items['MatchKey'] : strtotime(Carbon::now());
            $monitorDoc->Created_by = Auth::user()->username;
            $monitorDoc->created_at = Carbon::now();
            $monitorDoc->DocNum = $id;
            $monitorDoc->BLKey = $blKey;
            $monitorDoc->InvKey = $invDocEntry;
            $monitorDoc->InvDetailKey = $invDetailSubDocEntry;
            $monitorDoc->Flags = 'I';
            $monitorDoc->save();
            $docEntry = $monitorDoc->DocEntry;
        }
        return $docEntry;
    }

    public function getTenant($tenantKey)
    {
        return Tenant::where('DocEntry', '=', $tenantKey)->first();
    }
}
