<?php

namespace App\Exports;

use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Events\AfterSheet;
use Maatwebsite\Excel\Events\BeforeSheet;
use Maatwebsite\Excel\Sheet;

class ShipmentReportExport implements FromView, ShouldAutoSize, WithEvents
{
    /**
     * @return array
     */
    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event) {
                $cellRange = 'A1:W1'; // All headers
                $event->sheet->getDelegate()->getStyle($cellRange)->getFont()->setSize(12);
            },
            BeforeSheet::class => function (BeforeSheet $event) {
                Sheet::macro('styleCells', function (Sheet $sheet, string $cellRange, array $style) {
                    $sheet->getDelegate()->getStyle($cellRange)->applyFromArray($style);
                });
            }
        ];
    }


    /**
     * @return View
     */
    public function view(): View
    {
        $shipment = session('shipment');
        $shipment2 = session('shipment2');
        $fixYear = session('year');

        $previewReport = DB::select("EXEC Shipment_Price_Calculation '$shipment', '$shipment2', $fixYear");
        $previewReportSUM = DB::select("EXEC Shipment_Price_Calculation_SUM '$shipment', '$shipment2', $fixYear");

        $totalQty = 0;
        $totalPrice = 0;
        foreach ($previewReport as $item) {
            $totalQty+= $item->Qty_Ton;
            $totalPrice+= $item->Price;
        }

        return view(
            'export.report.shipment',
            [
                'doc' => $previewReport,
                'sum' => $previewReportSUM,
            ]
        );


        // Forget multiple keys...
        session()->forget(
            [
                "shipment",
                "year",
            ]
        );
    }
}
