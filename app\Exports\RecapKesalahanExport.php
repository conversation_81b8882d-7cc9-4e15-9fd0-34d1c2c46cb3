<?php

namespace App\Exports;

use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Events\AfterSheet;
use Maatwebsite\Excel\Events\BeforeSheet;
use Maatwebsite\Excel\Sheet;

class RecapKesalahanExport implements FromView, ShouldAutoSize, WithEvents, WithColumnFormatting
{
    public $data;
    public function __construct($data)
    {
        $this->data = $data;
    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event) {
                $cellRange = 'A1:W1'; // All headers
                $event->sheet->getDelegate()->getStyle($cellRange)->getFont()->setSize(12);
            },
            BeforeSheet::class => function (BeforeSheet $event) {
                Sheet::macro('styleCells', function (Sheet $sheet, string $cellRange, array $style) {
                    $sheet->getDelegate()->getStyle($cellRange)->applyFromArray($style);
                });
            }
        ];
    }

    public function columnFormats(): array
    {
        return [
            // 'AE' => '0.0', // Assuming column A
            // Add more column formats as needed
        ];
    }

    /**
     * @return View
     */
    public function view(): View
    {
        return view(
            'export.recap-kesalahan',
            [
                'data' => $this->data,
            ]
        );
    }
}
