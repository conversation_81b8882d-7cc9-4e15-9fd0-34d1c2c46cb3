<?php

namespace App\Models\Billing;

use Illuminate\Database\Eloquent\Model;
use OwenIt\Auditing\Contracts\Auditable;

/**
 * App\Models\Billing\HBIMP
 *
 * @property int $DocEntry
 * @property string $PortService
 * @property string $Status
 * @property string $YearArrival
 * @property int $Jetty
 * @property int $ImportID
 * @property int $CreatedBy
 * @property int $UpdatedBy
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string|null $DocNum
 * @property string|null $PostingDate
 * @property string|null $Remarks
 * @property string|null $PeriodDate
 * @property string|null $BillingNoteDate
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \OwenIt\Auditing\Models\Audit> $audits
 * @property-read int|null $audits_count
 * @method static \Illuminate\Database\Eloquent\Builder|HBIMP newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|HBIMP newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|HBIMP query()
 * @method static \Illuminate\Database\Eloquent\Builder|HBIMP whereBillingNoteDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HBIMP whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HBIMP whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HBIMP whereDocEntry($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HBIMP whereDocNum($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HBIMP whereImportID($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HBIMP whereJetty($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HBIMP wherePeriodDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HBIMP wherePortService($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HBIMP wherePostingDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HBIMP whereRemarks($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HBIMP whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HBIMP whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HBIMP whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HBIMP whereYearArrival($value)
 * @mixin \Eloquent
 */
class HBIMP extends Model
{
    // use \OwenIt\Auditing\Auditable;

    protected $primaryKey = 'DocEntry';
    protected $guarded = [];
    protected $table = 'BHIMP';
}
