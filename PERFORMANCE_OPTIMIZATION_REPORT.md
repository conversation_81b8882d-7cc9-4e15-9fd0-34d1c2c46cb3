# Billing Import Performance Optimization Report

## Problem Analysis

The `BillingImportController::store` method was taking 120+ seconds to execute due to several critical performance bottlenecks:

### 1. N+1 Query Problem (CRITICAL)
- `getTenantByName()` method called multiple times per detail item
- For 100 detail items: 400+ individual database queries
- Each call: `SELECT * FROM M_Tenant WHERE Name = ?`

### 2. Complex Queries in Loops
- `checkPortSeviceExist()` - Complex joins with date range comparisons
- `checkServiceLoadingExist()` - Similar complex queries
- `calcTotal()` and `calcTotalServiceLoading()` - Price calculation queries

### 3. Missing Database Indexes
- No index on `M_Tenant.Name` (frequently queried)
- No composite indexes on date range columns
- Missing indexes on foreign key relationships

### 4. Inefficient Delete Operations
- Subquery-based delete with correlated subqueries

## Implemented Solutions

### 1. Fixed N+1 Query Problem ✅
**Before:**
```php
foreach ($details as $index_bl => $items) {
    $this->checkPortSeviceExist($port_service, $vessel_arrive, $items['Tenant']);
    // Each call triggers: SELECT * FROM M_Tenant WHERE Name = ?
}
```

**After:**
```php
// Pre-load all tenant mappings once
$tenantNames = $details->pluck('Tenant')->unique()->filter();
$tenantMappings = DB::table('M_Tenant')
    ->whereIn('Name', $tenantNames)
    ->pluck('DocEntry', 'Name')
    ->toArray();

foreach ($details as $index_bl => $items) {
    $this->checkPortSeviceExist($port_service, $vessel_arrive, $items['Tenant'], $tenantMappings);
    // Uses cached mapping - no database query
}
```

### 2. Added Database Indexes ✅
Created migration: `2025_08_03_170000_add_tenant_name_index.php`

```sql
-- Critical indexes added:
CREATE INDEX idx_m_tenant_name ON M_Tenant(Name);
CREATE INDEX idx_mplps_period ON MPLPS(PeriodStart, PeriodEnd);
CREATE INDEX idx_mplsl_period ON MPLSL(PeriodStart, PeriodEnd);
CREATE INDEX idx_bexp_docentry ON BEXP(DocEntry);
```

### 3. Updated Method Signatures
Modified methods to accept cached tenant mappings:
- `checkPortSeviceExist($portService, $vessel_arrive, $tenant, $tenantMappings = [])`
- `checkServiceLoadingExist($vessel_arrive, $tenant, $tenantMappings = [])`
- `storeBilling($i, $items, $request, $id, $type, $tenantMappings = [])`

## Additional Recommendations

### 1. Optimize Delete Operation (HIGH PRIORITY)
**Current inefficient code:**
```php
DB::table('BHIMP')
    ->whereRaw('
        (
            SELECT COUNT(*) FROM BEXP WHERE DocNum = BHIMP.DocEntry
        ) = 0
    ')
    ->where('Status', '=', 'Open')
    ->delete();
```

**Recommended optimization:**
```php
// Use LEFT JOIN instead of correlated subquery
DB::table('BHIMP as b')
    ->leftJoin('BEXP as e', 'e.DocNum', '=', 'b.DocEntry')
    ->whereNull('e.DocNum')
    ->where('b.Status', '=', 'Open')
    ->delete();
```

### 2. Batch Operations (MEDIUM PRIORITY)
Consider using bulk insert/update operations instead of individual saves:
```php
// Instead of multiple individual saves
foreach ($items as $item) {
    $model = new BEXP();
    $model->fill($item);
    $model->save();
}

// Use bulk insert
BEXP::insert($itemsArray);
```

### 3. Database Query Optimization (MEDIUM PRIORITY)
- Add indexes on frequently filtered columns
- Consider query result caching for static data
- Use EXPLAIN PLAN to identify slow queries

### 4. Additional Indexes to Consider
```sql
-- For BHIMP table
CREATE INDEX idx_bhimp_importid_status ON BHIMP(ImportID, Status);
CREATE INDEX idx_bhimp_docnum ON BHIMP(DocNum);

-- For BEXP table  
CREATE INDEX idx_bexp_docnum ON BEXP(DocNum);
CREATE INDEX idx_bexp_exportid ON BEXP(ExportId);

-- For price list queries
CREATE INDEX idx_mplps_tenantkey_type_period ON MPLPS(TenantKey, Type, PeriodStart, PeriodEnd);
CREATE INDEX idx_mplsl_tenantkey_type_period ON MPLSL(TenantKey, Type, PeriodStart, PeriodEnd);
```

## Expected Performance Improvement

### Before Optimization:
- 120+ seconds execution time
- 400+ database queries for tenant lookups
- Multiple complex queries per detail item
- Table locks causing blocking

### After Optimization:
- **Expected: 10-30 seconds** (75-90% improvement)
- Single query for all tenant lookups
- Faster index-supported queries
- Reduced database contention

## Next Steps

1. **Deploy the migration** to add database indexes
2. **Test the optimized code** with production-like data volume
3. **Monitor query performance** using SQL Server profiler
4. **Implement additional optimizations** if needed
5. **Consider caching** for frequently accessed static data

## Monitoring

Monitor these metrics after deployment:
- Execution time of `BillingImportController::store`
- Database query count per request
- SQL Server lock duration and blocking
- Memory usage during bulk operations

## Files Modified

1. `app/Http/Controllers/Billing/BillingImportController.php` - Added tenant caching
2. `app/Traits/BillingHelper.php` - Updated method signatures
3. `database/migrations/2025_08_03_170000_add_tenant_name_index.php` - New indexes
