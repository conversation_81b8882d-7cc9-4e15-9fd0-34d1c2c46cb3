<?php

namespace App\Models\Billing;

use App\Models\Tenant\Tenant;
use Illuminate\Database\Eloquent\Model;
use OwenIt\Auditing\Contracts\Auditable;

/**
 * App\Models\Billing\BEXP
 *
 * @property int $DocEntry
 * @property int $DocNum
 * @property string $NoBL
 * @property string|null $DateBL
 * @property int $TenantId
 * @property int $BCType
 * @property string|null $LoadingItem
 * @property string $LoadingQty
 * @property string|null $Remark
 * @property string $Deleted
 * @property string|null $Status
 * @property string|null $PortServiceType
 * @property string|null $LoadingUnloadingType
 * @property int $CreatedBy
 * @property int $UpdatedBy
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string|null $Unit
 * @property string|null $Notification
 * @property string|null $ItemName
 * @property string|null $CompanyHeader
 * @property string $Total
 * @property string|null $Signature1
 * @property string|null $Signature2
 * @property string|null $Signature3
 * @property int|null $BP
 * @property int|null $Jetty
 * @property string|null $AttachmentText
 * @property string $Type
 * @property string|null $Price
 * @property string|null $ServiceLoading
 * @property string|null $Classification
 * @property string $WeightCategory
 * @property string $TotalServiceLoading
 * @property string|null $CurrencyPortService
 * @property string|null $CurrencyServiceLoading
 * @property int|null $BaseId
 * @property int|null $Agent
 * @property string|null $BaseIdString
 * @property string|null $NetWeight
 * @property string|null $UnitPrice
 * @property string|null $TotalInv
 * @property string|null $StatusServiceLoading
 * @property string|null $QtyEstimate
 * @property string|null $PriceEstimate
 * @property string|null $BillingType
 * @property string|null $ChargeTo
 * @property string|null $TaxCode
 * @property string|null $QtyRevised
 * @property string|null $PriceRevised
 * @property string|null $NoNota
 * @property string|null $TotalEstimate
 * @property string|null $TotalRevised
 * @property string|null $NoNotaSl
 * @property int|null $LineNum
 * @property string|null $IsTenant
 * @property string|null $SubTotal
 * @property string|null $VatValue
 * @property string|null $VatValueSL
 * @property string|null $SubTotalSL
 * @property string|null $TotalEstimateSL
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \OwenIt\Auditing\Models\Audit> $audits
 * @property-read int|null $audits_count
 * @property-read Tenant|null $tenant
 * @method static \Illuminate\Database\Eloquent\Builder|BEXP newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|BEXP newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|BEXP query()
 * @method static \Illuminate\Database\Eloquent\Builder|BEXP whereAgent($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BEXP whereAttachmentText($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BEXP whereBCType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BEXP whereBP($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BEXP whereBaseId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BEXP whereBaseIdString($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BEXP whereBillingType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BEXP whereChargeTo($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BEXP whereClassification($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BEXP whereCompanyHeader($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BEXP whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BEXP whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BEXP whereCurrencyPortService($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BEXP whereCurrencyServiceLoading($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BEXP whereDateBL($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BEXP whereDeleted($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BEXP whereDocEntry($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BEXP whereDocNum($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BEXP whereIsTenant($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BEXP whereItemName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BEXP whereJetty($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BEXP whereLineNum($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BEXP whereLoadingItem($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BEXP whereLoadingQty($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BEXP whereLoadingUnloadingType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BEXP whereNetWeight($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BEXP whereNoBL($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BEXP whereNoNota($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BEXP whereNoNotaSl($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BEXP whereNotification($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BEXP wherePortServiceType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BEXP wherePrice($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BEXP wherePriceEstimate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BEXP wherePriceRevised($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BEXP whereQtyEstimate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BEXP whereQtyRevised($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BEXP whereRemark($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BEXP whereServiceLoading($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BEXP whereSignature1($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BEXP whereSignature2($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BEXP whereSignature3($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BEXP whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BEXP whereStatusServiceLoading($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BEXP whereSubTotal($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BEXP whereSubTotalSL($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BEXP whereTaxCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BEXP whereTenantId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BEXP whereTotal($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BEXP whereTotalEstimate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BEXP whereTotalEstimateSL($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BEXP whereTotalInv($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BEXP whereTotalRevised($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BEXP whereTotalServiceLoading($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BEXP whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BEXP whereUnit($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BEXP whereUnitPrice($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BEXP whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BEXP whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BEXP whereVatValue($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BEXP whereVatValueSL($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BEXP whereWeightCategory($value)
 * @mixin \Eloquent
 */
class BEXP extends Model
{
    // use \OwenIt\Auditing\Auditable;

    protected $primaryKey = 'DocEntry';
    protected $guarded = [];
    protected $table = 'BEXP';

    public function tenant()
    {
        return $this->belongsTo(Tenant::class, 'TenantId', 'DocEntry');
    }
}
