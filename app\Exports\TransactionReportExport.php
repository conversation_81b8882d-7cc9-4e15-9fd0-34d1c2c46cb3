<?php

namespace App\Exports;

use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Events\AfterSheet;
use Maatwebsite\Excel\Events\BeforeSheet;
use Maatwebsite\Excel\Sheet;

class TransactionReportExport implements FromView, WithEvents, WithColumnWidths, ShouldAutoSize
{
    private $summaries;
    private $currency;
    private $report_type;

    /**
     * ServiceLoadingPortServiceExport constructor.
     * @param $summaries
     * @param $currency
     * @param $report_type
     */
    public function __construct($summaries, $currency, $report_type)
    {
        $this->summaries = $summaries;
        $this->currency = $currency;
        $this->report_type = $report_type;
    }

    /**
     * @return array
     */
    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event) {
                $cellRange = 'A1:W1'; // All headers
                $event->sheet->getDelegate()->getStyle($cellRange)->getFont()->setSize(12);
            },
            BeforeSheet::class => function (BeforeSheet $event) {
                Sheet::macro('styleCells', function (Sheet $sheet, string $cellRange, array $style) {
                    $sheet->getDelegate()->getStyle($cellRange)->applyFromArray($style);
                });
            }
        ];
    }

    public function view(): View
    {
        if ($this->report_type == 'KB Local Report') {
            return view('export.report.kb_local_report', [
                'summaries' => $this->summaries,
                'currency' => $this->currency,
                'report_type' => $this->report_type,
            ]);
        } else {
            return view('export.report.kb_local_report', [
                'summaries' => $this->summaries,
                'currency' => $this->currency,
                'report_type' => $this->report_type,
            ]);
        }
    }

    public function columnWidths(): array
    {
        return [
            'J' => 30,
            'K' => 30,
        ];
    }
}
