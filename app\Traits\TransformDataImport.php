<?php

namespace App\Traits;

trait TransformDataImport
{
    /**
     * @param $details
     *
     * @return array
     */
    public function transformDetails($details): array
    {
        $convertArray = [];
        foreach ($details as $index_bl => $items) {
            foreach ($items as $index2 => $value) {
                $header = $this->keyDetails();
                foreach ($header as $index => $valueHeader) {
                    if ($index == $index2) {
                        $convertArray[$index_bl][$valueHeader] = $value;
                    }
                }
            }
        }
        return $convertArray;
    }

    public function transformSingleRow($details): array
    {
        $convertArray = [];
        foreach ($details as $index2 => $value) {
            $header = $this->keyDetails();
            foreach ($header as $index => $valueHeader) {
                if ($index == $index2) {
                    $convertArray[$valueHeader] = $value;
                }
            }
        }
        return $convertArray;
    }

    /**
     * @return string[]
     */
    protected function keyDetails(): array
    {
        return [
            'No_bl',
            'Date_bl',
            'Notes',
            'Process',
            'Type',
            'Confirmation',
            'IsUrgent',
            'Bc16',
            'Tenant_name',
            'BP',
            'PortOfLoading',
            'Expired_date',
            'InsuranceCurrency',
            'InsuranceValue',
            'WarehouseName',
            // 'Ocean_freight',
            'Currency',
            'Freight_value',
            'ContainerNo',
            'isFeOri',
            'CBM',
            'No_inv',
            'AJU_No',
            'Ebilling_date',
            'BillingDate',
            'PIB_No',
            'PIB_date',
            'SPPB_No',
            'SPPB_date',
            'SPPD_No',
            'SPPD_date',
            'AJU_No_16',
            'PIB_No_16',
            'PIB_date_16',
            'SPPB_No_16',
            'SPPB_date_16',
            'SPPD_No_16',
            'SPPD_date_16',
            'EmailToBcDate',
            'Attachment',
            'Remarks',
            'Flags',
            'DocEntry',
            'TemporaryExportId',
            // 'isOriginal',
            'CountAttachmentSurat',
            'ItemName',
            'ItemQty',
            'UnitQty',
            'GrossWeight',
            'UnitWeight',
            'IsParent',
            'LetterNo',
            'PPJKCode',
            'E-Sign',
            'Draft',
            'SecretKey',
            'CountAttachment',
            'Created_by',
            'Status',
            'Process2',
            'NonKB',
            'MatchKey',
            'CountInv',
            'Logs',
            'LineNum',
            'isChange',
            'SPPBStatus',
            'CountNotul',
            'Rate',
            'BM',
            'BMTP',
            'BMAD',
            'PPN',
            'PPH',
            'CountStockpiledReport',
            'ExportItemName',
            // 'IncreaseValue',
            // 'DecreaseValue',
        ];
    }
}
