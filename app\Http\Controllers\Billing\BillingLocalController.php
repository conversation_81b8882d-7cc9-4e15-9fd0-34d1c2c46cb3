<?php

namespace App\Http\Controllers\Billing;

use App\Http\Controllers\Controller;
use App\Models\Billing\BEXP;
use App\Models\Billing\BEXP1;
use App\Models\Billing\BHLOCAL;
use App\Models\ExchangeRate\ExchangeRate;
use App\Traits\BillingHelper;
use App\Traits\ImportHelper;
use App\Traits\PostingPeriodHelper;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class BillingLocalController extends Controller
{
    use PostingPeriodHelper;
    use ImportHelper, BillingHelper;

    public function __construct()
    {
        $this->middleware(['direct_permission:Local-index'])->only(['index', 'dataBilling', 'getExportData', 'dataCargo']);
        $this->middleware(['direct_permission:Local-store'])->only(['store', 'storeExportDetails']);
        $this->middleware(['direct_permission:Local-edits'])->only(['update', 'cancelDoc']);
        $this->middleware(['direct_permission:Local-erase'])->only('destroy');
    }

    public function index(Request $request)
    {
        $pagination = (object) $request->pagination;
        $year_local = date('Y');
        $pages = isset($pagination->page) ? (int) $pagination->page : 1;
        $filter = isset($request->filter) ? (string) $request->filter : $year_local;
        $row_data = isset($request->itemsPerPage) ? (int) $request->itemsPerPage : 20;
        // $sorts = isset($request->sort) ? (string)$request->sort : "Shipment";
        // $order = isset($request->order) ? (string)$request->order : "desc";
        $sorts = isset($request->sortDesc[0]) ? $request->sortBy[0]['key'] : 'DocNum';
        $order = isset($request->sortDesc[0]) ? $request->sortBy[0]['order'] : 'desc';
        $data_status = isset($request->dataStatus) ? (string) $request->dataStatus : 'Open';

        $search = isset($request->q) ? (string) $request->q : '';
        $select_data = isset($request->selectData) ? (string) $request->selectData : 'T0.DocNum';
        $offset = $request->page;
        $row_data = ($request->itemsPerPage) ? $request->itemsPerPage : 1000;
        $username = $request->user()->username;
        $permission_id = $request->user()->permission_id;

        $result = [];
        $query = DB::table('BHLOCAL AS T0')
            ->selectRaw("
                T0.*
                ,CASE
                    WHEN T2.VesselType = 'TB' THEN
                        (SELECT Name FROM M_CARGO WHERE DocEntry = T2.VesselName)
                    ELSE
                        (SELECT Name FROM M_CARGO WHERE DocEntry = T2.VesselName)
                    END AS VesselName
                ,T2.Shipment
                ,CONVERT(VARCHAR, T2.VesselArrival, 106) AS VesselArrival
                ,CONVERT(VARCHAR, T2.VesselDeparture, 106) AS VesselDeparture
                ,T2.PortOrigin
                ,CASE
                    WHEN T0.Status = 'Open' THEN '#CFD8DC'
                    WHEN T0.Status = 'Cancel' THEN 'red'
                    WHEN T0.Status = 'Closed' THEN '#43A047'
                END AS Color
                ,T2.DestinationPort
                --,CONCAT(YEAR(T0.PeriodDate), '-', MONTH(T0.PeriodDate)) as PeriodDate
                ,T3.name AS Created_by
                --,CONVERT(VARCHAR, T0.PostingDate, 106) AS PostingDate
                ,T2.Voyage
            ")
            ->leftJoin('L_Master As T2', 'T2.DocEntry', 'T0.LocalID')
            ->leftJoin('M_CARGO AS T1', 'T1.DocEntry', 'T2.VesselName')
            ->leftJoin('users AS T3', 'T3.id', 'T0.CreatedBy')
            ->when($permission_id, function ($query) use ($select_data, $search, $permission_id, $filter, $sorts, $order, $data_status) {
                if ($data_status == 'All') {
                    $data_query = $query->whereRaw(
                        "T0.Status IN ('Open', 'Open-upload')"
                    );
                } else {
                    $data_query = $query->whereRaw(
                        "T0.Status LIKE '%${data_status}'"
                    );
                }

                if ($select_data == 'BL') {
                    $data_query = $query->whereRaw(
                        "T0.DocEntry IN ( SELECT X0.DocNum
                            FROM TEXP AS X0
                            WHERE X0.No_bl LIKE '%${search}%'
                        )"
                    );
                }

                if ($select_data == 'Vessel Name') {
                    $data_query = $query->whereRaw(
                        "T1.Name LIKE '%${search}%'"
                    );
                }

                if ($select_data == 'Tenant Name') {
                    $data_query = $query->whereRaw(
                        "(
                            select count(*)
                            from M_Tenant as t
                            left join BEXP as b on t.DocEntry = b.TenantId
                            where t.Name LIKE '%${search}%'
                            and b.DocNum = T0.DocEntry
                            and b.Type = 'Import'
                        ) > 0"
                    );
                }

                if ($select_data == 'Tenant') {
                    $data_query = $query->leftJoin("BEXP as detail", "detail.DocNum", "T0.DocEntry")
                        ->leftJoin("M_Tenant as tenant", "tenant.DocEntry", "detail.TenantId")
                        ->where("detail.Type", "=", "T0.Type")
                        ->where("tenant.Name", "LIKE", "'%${search}%'");
                }

                if ($select_data == 'Voyage') {
                    $data_query = $query->whereRaw(
                        "T2.Voyage LIKE '%${search}%'"
                    );
                }

                if ($select_data && $select_data != 'BL' && $select_data != 'Tenant Name' && $select_data != 'Vessel Name' && $select_data != 'Voyage' && $select_data != 'Tenant' && $search) {
                    $data_query = $query->whereRaw("
                        T0.${select_data} LIKE '%${search}%'
                    ");
                }

                if ($filter == 'All') {
                    $data_query = $query->whereRaw(
                        "T2.VesselDeparture LIKE '%%'"
                    );
                } else {
                    // $data_query = $query->whereRaw(
                    //     "T2.VesselDeparture LIKE '%${filter}%'"
                    // );
                }

                if ($permission_id != '1') {
                    $data_query = $query->where('T2.Deleted', '=', 'N');
                }

                if ($sorts == 'Shipment') {
                    $data_query = $query->orderBy(DB::raw('YEAR(VesselDeparture)'), 'DESC')
                        ->orderBy(DB::raw("
                            CASE ISNUMERIC(T2.[Shipment])
                                WHEN 1 THEN REPLICATE('0', 100 - LEN(T2.[Shipment])) + T2.[Shipment]
                                ELSE T2.[Shipment]
                            END
                        "), 'DESC');
                } elseif ($sorts == 'DocNum') {
                    $data_query = $query->orderBY('T0.DocNum', $order);
                } else {
                    $data_query = $query->orderBY($sorts, $order);
                }
                return $data_query;
            });

        $result['total'] = $query->count();
        $import = $query->paginate($row_data)
            ->items();

        $year_shipments = DB::table('L_Master')
            ->selectRaw('YEAR(VesselArrival) AS Year')
            ->orderBy(DB::raw('YEAR(VesselArrival)'), 'DESC')
            ->distinct()
            ->get();

        $year_arr = [];
        foreach ($year_shipments as $year_shipment) {
            $year_arr[] = [
                'Name' => $year_shipment->Year,
            ];
        }

        $filter = array_merge([['Name' => 'All']], $year_arr);

        $status = DB::table('BHLOCAL')
            ->distinct()
            ->select("Status")
            ->pluck('Status')
            ->toArray();

        $item_search = [
            'BL',
            'Shipment',
            'DocNum',
            'Vessel Name',
            'Tenant',
            'Voyage',
            'Tenant Name'
        ];

        $result = array_merge($result, [
            'rows' => $import,
            'filter' => $filter,
            'status' => array_merge(['All'], $status),
            'item_search' => $item_search,
            'year_local' => $year_local
        ]);
        return response()->json($result);
    }

    /**
     * @param Request $request
     * @param $docNum
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function dataBilling(Request $request, $docNum)
    {
        $data_type = $request->trans_type;
        $index = $this->getPrevAndNext($docNum, 'BHLOCAL', 'DocEntry');

        $query_header = DB::table('BHLOCAL AS T2')
            ->selectRaw("
                    T0.*
                    ,T2.*
                    ,CASE
                    WHEN T0.VesselType = 'TB' THEN
                        (SELECT Name FROM M_CARGO WHERE DocEntry = T0.VesselName)
                    ELSE
                        (SELECT Name FROM M_CARGO WHERE DocEntry = T0.VesselName)
                    END AS VesselName
                    ,CASE
                        WHEN CAST(T0.Shipment AS NVARCHAR) = '0' THEN '-'
                        ELSE T0.Shipment
                    END AS Shipment
                    ,CONVERT(varchar,T0.VesselArrival,20) as VesselArrival
                    ,CONVERT(varchar,T0.FinishUnloadingDate,20) as FinishUnloadingDate
                    ,CONVERT(varchar,T0.UnloadingDate,20) as UnloadingDate
                    ,CONVERT(varchar,T0.TglSandar,20) as BerthingDate
                    ,CONVERT(varchar,T0.TglLabuh,20) as AnchorageDate
                    ,CONVERT(VARCHAR, T0.VesselDeparture, 106) AS VesselDeparture
                    ,T0.PortOrigin AS PortOrigin
                    ,T0.DestinationPort
                    ,CONVERT(VARCHAR, T0.PostingDate, 106) AS PostingDate
                    ,T0.Voyage
                    ,T2.LocalID AS ExportId
                    ,T0.GrossWeight
                    ,T2.Status
                    ,T0.DocEntry AS VesselDocEntry
                    ,T0.DocNum AS VesselDocNum
                    ,'INDONESIA' AS VesselFlag
                    ,T2.Type As TransType
                    --,T2.ExportID as ExportId
                    ,T3.Name As Jetty
                    ,T3.Port
                    ,(select name from M_CARGO Z where Z.DocEntry= T0.tongkang )as tongkang
                    ,FORMAT( (
                        SELECT SUM(
                            ROUND(B.LoadingQty, 5)
                        )
                        FROM BEXP AS B
                        WHERE B.DocNum = T2.DocEntry
                        ), '###.#####') AS GrossWeight
                ")
            ->leftJoin('L_Master AS T0', 'T0.DocEntry', 'T2.LocalID')
            ->leftJoin('M_Jetty AS T3', 'T3.DocEntry', 'T2.Jetty')
            ->whereRaw("T2.DocEntry='${docNum}'")
            ->first();

        $permission_id = $request->user()->permission_id;
        $user_id = $request->user()->id;
        $open_time = str_replace('.', '', microtime(true));
        session(['openDateTime' => $open_time]);

        if ($query_header) {
            $query = DB::table('BEXP AS T0')
                ->leftJoin('M_TBC AS T1', 'T1.DocEntry', 'T0.BCType')
                ->leftJoin('M_Tenant AS T2', 'T2.DocEntry', 'T0.TenantId')
                ->leftJoin('users AS T3', 'T3.id', 'T0.CreatedBy')
                ->leftJoin('M_BP AS T4', 'T4.DocEntry', 'T0.BP')
                ->leftJoin('M_Jetty AS T5', 'T5.DocEntry', 'T0.Jetty')
                ->leftJoin('M_Agent AS T6', 'T6.DocEntry', 'T0.Agent')
                ->selectRaw("
                    T0.*
                    ,T2.Name AS Tenant
                    ,T2.FullName AS TenantFullName
                    ,T4.Name AS BP
                    ,T6.Name AS Agent
                    ,T1.Type AS BCType
                    ,T2.IsTenant
                    ,CONVERT(VARCHAR, T0.DateBL, 105) AS DateBL
                    ,CAST(T0.LoadingQty AS DECIMAL(20, 5)) as LoadingQty
                    ,(
                        SELECT distinct CONCAT(ItemCode, ' - ' , FrgnName) FROM MPS WHERE ItemCode=T0.PortServiceType
                    ) AS PortServiceType
                    --,T0.PortServiceType
                    ,(
                        SELECT COUNT(*) FROM BEXP1 as x
                        WHERE x.ExportId = T0.DocEntry
                    )AS CountDetails
                    ,T5.Name As Jetty
                    ,T5.Port
                    ,(
                        SELECT COUNT(*) FROM M_Doc_attachment as x
                        WHERE x.M_doc_key = T0.DocEntry
                        AND x.DocType = 'Billing'
                        AND x.TransType='BillingDetail'
                    )AS CountAttachment
                ")
                ->where('T0.DocNum', '=', $docNum)
                ->where('T0.Type', '=', $data_type)
                ->where('T0.Deleted', '=', 'N')
                ->orderBy('T0.BaseId')
                ->orderBy('T0.DocEntry')
                ->get();

            $open_date_time = str_replace('.', '', microtime(true));

            return response()->json([
                'header' => $query_header,
                'rows' => $query,
                'openDateTime' => $open_date_time,
                'docEntry' => $index ? $index->DocEntry : $docNum,
                'prev' => $index ? $index->prev : 0,
                'next' => $index ? $index->next : 0,
                'runCBMValidation' => 'Y',
            ]);
        }
    }

    /**
     * @param Request $request
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function dataCargo(Request $request)
    {
        $trans_type = $request->trans_type;
        $vessel_type = $request->vessel_type;
        $year_now = date('Y');
        $month_now = date('m');
        $vessel = DB::table('L_Master AS T1')
            //->leftJoin("M_CARGO AS T2", "T1.VesselName", "T2.DocEntry")
            ->selectRaw("
                CASE
                    WHEN T1.VesselType = 'TB' THEN
                        (SELECT Name FROM M_CARGO WHERE DocEntry = T1.VesselName)
                    ELSE
                        (SELECT Name FROM M_CARGO WHERE DocEntry = T1.VesselName)
                END AS Name,
                T1.VesselType,
                T1.VesselName
            ")
            // ->where("T1.DocStatus", "=", "Open")
            ->where('T1.TransType', '=', $trans_type)
            ->where('T1.VesselType', '=', $vessel_type)
            // ->whereRaw("YEAR(T1.VesselDeparture) >= ${year_now}")
            ->whereRaw("T1.DocEntry NOT IN (SELECT LocalId FROM BHLOCAL
                WHERE Status = 'Open' OR Status = 'Open-upload')")
            ->distinct()
            ->get();

        return response()->json([
            'rows' => $vessel,
        ]);
    }

    /**
     * @param Request $request
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function voyageBYVessel(Request $request)
    {
        $year_now = date('Y');
        $month_now = date('m');
        $voyage = DB::table('L_Master AS T1')
            ->selectRaw('convert(varchar, T1.VesselDeparture, 106) AS Name, T1.DocEntry')
            ->where('T1.VesselName', '=', $request->vesselName)
            ->where('T1.VesselType', '=', $request->vesselType)
            ->where('T1.TransType', '=', $request->transType)
            // ->whereRaw("YEAR(T1.VesselDeparture) >= ${year_now}")
            ->whereRaw("T1.DocEntry NOT IN (SELECT LocalId FROM BHLOCAL WHERE Status = 'Open' OR Status = 'Open-upload')")
            // ->where("T1.DocStatus", "=", "Open")
            ->distinct()
            ->get();

        return response()->json([
            'rows' => $voyage,
        ]);
    }

    public function getPartial(Request $request)
    {
        $billing_id = $request->billingId;
        $base_id = $request->arrayBaseId;
        $query = DB::table("BHLOCAL AS T0")
            ->leftJoin("L_Master AS T1", "T0.LocalID", "T1.DocEntry")
            ->leftJoin("T_MDOC AS T2", "T1.DocEntry", "T2.DocNum")
            ->leftJoin('M_Tenant AS T3', 'T3.DocEntry', 'T2.Tenant_key')
            ->leftJoin('M_BP AS T4', 'T4.DocEntry', 'T2.BP')
            ->leftJoin('M_Agent AS T5', 'T5.DocEntry', 'T2.Agent')
            ->leftJoin('M_Jetty AS T6', 'T6.DocEntry', 'T1.Jetty')
            ->where("T2.DocType", "=", "Local")
            ->where("T2.Deleted", "=", "N")
            ->whereRaw("T2.DocEntry NOT IN (SELECT BaseId FROM BEXP WHERE DocNum = T0.DocEntry AND Type='Local')")
            ->where("T0.DocEntry", "=", $billing_id)
            ->whereNotIn("T2.DocEntry", $base_id)
            ->selectRaw("
                CAST ('0'+CAST(
                        CASE
                            WHEN UPPER(T2.UnitWeight) = 'KGS'  THEN T2.ItemQty / 1000
                            WHEN UPPER(T2.UnitWeight) = 'KG' THEN T2.ItemQty / 1000
                            ELSE T2.ItemQty
                        END AS DECIMAL(20, 5)
                    ) AS NVARCHAR) as LoadingQty
                ,T2.ItemName AS LoadingItem
                ,ROW_NUMBER() OVER (Order by T2.DocEntry) AS RowNumber
                ,T6.Name As Jetty
                ,T3.Name AS Tenant
                ,T4.Name AS BP
                ,T5.Name AS Agent
                ,T2.No_bl AS NoBL
                ,CONVERT(VARCHAR, T2.Date_bl, 105) AS DateBL
                ,T2.AJU_No
                ,T2.ItemName AS LoadingItem
                ,T2.UnitWeight
                ,T2.REG_No
                ,T2.SPPB_no AS SPE_No
                ,NULL AS DocEntry
                ,(
                    SELECT TOP 1 BilledBy FROM MPLTENANT WHERE TenantKey = T2.Tenant_key
                ) AS CompanyHeader
                , 'Yes' AS LoadingUnloadingType
                , 'B/L Weight' AS WeightCategory
                ,CONVERT(VARCHAR, T2.REG_date, 105) AS REG_date
                ,CONVERT(VARCHAR, T2.Ebilling_date, 105) AS Ebilling_date
                ,'attachment' AS Attachment
                ,CONVERT(VARCHAR, T2.SPPB_date, 105) AS SPE_date
                ,T2.Created_by
                ,NULL AS Notification
                ,T2.Remarks
                ,0 AS CountDetails
                ,'Open' AS Status
                ,T2.DocNum
                ,T2.DocEntry As BaseId
                ,CASE
                    WHEN (SELECT COUNT(*) FROM MPLSL
                            WHERE TenantKey = T2.Tenant_key AND Type ='Tenant'
                            AND CONVERT(varchar,T1.VesselDeparture,20) >= PeriodStart AND CONVERT(varchar,T1.VesselDeparture,20) <= PeriodEnd) > 0
                        THEN (
                            SELECT Top 1 Price
                            FROM MPLSL
                            WHERE TenantKey=T2.Tenant_key AND Type ='Tenant'
                            AND CONVERT(varchar,T1.VesselDeparture,20) >= PeriodStart AND CONVERT(varchar,T1.VesselDeparture,20) <= PeriodEnd
                        )
                    ELSE
                        (
                            SELECT Top 1 Price
                            FROM MPLSL
                            WHERE Type ='Global'
                            AND CONVERT(varchar,T1.VesselDeparture,20) >= PeriodStart AND CONVERT(varchar,T1.VesselDeparture,20) <= PeriodEnd
                        )
                END AS ServiceLoading
                ,CASE
                    WHEN (SELECT COUNT(*) FROM MPLPS
                            WHERE TenantKey = T2.Tenant_key AND Type ='Tenant'
                            AND CONVERT(varchar,T1.VesselDeparture,20) >= PeriodStart AND CONVERT(varchar,T1.VesselDeparture,20) <= PeriodEnd
                            ) > 0
                        THEN (
                            SELECT Top 1 Price
                            FROM MPLPS
                            WHERE TenantKey=T2.Tenant_key AND Type ='Tenant'
                            AND CONVERT(varchar,T1.VesselDeparture,20) >= PeriodStart AND CONVERT(varchar,T1.VesselDeparture,20) <= PeriodEnd

                        )
                    ELSE
                        0
                END AS Price
                ,CASE
                    WHEN (SELECT COUNT(*) FROM MPLSL
                            WHERE TenantKey = T2.Tenant_key AND Type ='Tenant'
                            AND CONVERT(varchar,T1.VesselDeparture,20) >= PeriodStart AND CONVERT(varchar,T1.VesselDeparture,20) <= PeriodEnd) > 0
                        THEN (
                            SELECT Top 1 B.Currency
                            FROM MPLSL AS A
                            LEFT JOIN M_Currency AS B ON A.Currency = B.DocEntry
                            WHERE A.TenantKey=T2.Tenant_key AND A.Type ='Tenant'
                            AND CONVERT(varchar,T1.VesselDeparture,20) >= A.PeriodStart AND CONVERT(varchar,T1.VesselDeparture,20) <= A.PeriodEnd
                        )
                    ELSE
                        (
                            SELECT Top 1 B.Currency
                            FROM MPLSL AS A
                            LEFT JOIN M_Currency AS B ON A.Currency = B.DocEntry
                            WHERE  A.Type ='Global'
                            AND CONVERT(varchar,T1.VesselDeparture,20) >= A.PeriodStart AND CONVERT(varchar,T1.VesselDeparture,20) <= A.PeriodEnd
                        )
                END AS CurrencyServiceLoading
                ,CASE
                    WHEN (SELECT COUNT(*) FROM MPLPS
                            WHERE TenantKey = T2.Tenant_key AND Type ='Tenant'
                            AND CONVERT(varchar,T1.VesselDeparture,20) >= PeriodStart AND CONVERT(varchar,T1.VesselDeparture,20) <= PeriodEnd
                            ) > 0
                        THEN (
                            SELECT Top 1 B.Currency
                            FROM MPLPS AS A
                            LEFT JOIN M_Currency AS B ON A.Currency = B.DocEntry
                            WHERE A.TenantKey=T2.Tenant_key AND A.Type ='Tenant'
                            AND CONVERT(varchar,T1.VesselDeparture,20) >= A.PeriodStart AND CONVERT(varchar,T1.VesselDeparture,20) <= A.PeriodEnd

                        )
                    ELSE
                        ''
                END AS CurrencyPortService
                ,CASE
                    WHEN (SELECT COUNT(*) FROM MPLPS
                            WHERE TenantKey = T2.Tenant_key AND Type ='Tenant'
                            AND CONVERT(varchar,T1.VesselDeparture,20) >= PeriodStart AND CONVERT(varchar,T1.VesselDeparture,20) <= PeriodEnd) > 0
                        THEN (
                            SELECT Top 1 CONCAT(B.ItemCode, ' - ', B.FrgnName)
                            FROM MPLPS AS A
                            LEFT JOIN MPS AS B ON B.DocEntry = A.PSKey
                            WHERE A.TenantKey=T2.Tenant_key AND A.Type ='Tenant'
                            AND CONVERT(varchar,T1.VesselDeparture,20) >= A.PeriodStart AND CONVERT(varchar,T1.VesselDeparture,20) <= A.PeriodEnd
                        )
                    ELSE
                        ''
                END AS PortServiceType

                --,T0.DocStatus
                , 'Guo Yao Min' AS Signature1
            ")
            ->get();
        return response()->json([
            'rows' => $query
        ]);
    }

    /**
     * @param Request $request
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getExportData(Request $request)
    {
        $query_header = DB::table('L_Master AS T0')
            ->selectRaw("
                    T0.DocEntry AS ExportId
                    ,T0.*
                    ,CASE
                    WHEN T0.VesselType = 'TB' THEN
                        (SELECT Name FROM M_CARGO WHERE DocEntry = T0.VesselName)
                    ELSE
                        (SELECT Name FROM M_CARGO WHERE DocEntry = T0.VesselName)
                    END AS VesselName
                    ,CASE
                        WHEN CAST(T0.Shipment AS NVARCHAR) = '0' THEN '-'
                        ELSE T0.Shipment
                    END AS Shipment
                    ,CONVERT(varchar,T0.VesselArrival,20) as VesselArrival
                    ,CONVERT(varchar,T0.FinishUnloadingDate,20) as FinishUnloadingDate
                    ,CONVERT(varchar,T0.UnloadingDate,20) as UnloadingDate
                    ,CONVERT(varchar,T0.TglSandar,20) as BerthingDate
                    ,CONVERT(varchar,T0.TglLabuh,20) as AnchorageDate
                    ,CONVERT(VARCHAR, T0.PostingDate, 106) AS PostingDate
                    ,CONVERT(VARCHAR, T0.VesselDeparture, 106) AS VesselDeparture
                    ,NUll As VesselFlag
                    ,T3.Name As Jetty
                    ,T3.Port
                    ,(select name from M_CARGO Z where Z.DocEntry= T0.tongkang )as tongkang
                    ,T0.voyage AS Voyage
                    ,NULL AS DocEntry
                    ,'Local' AS DocumentType
                    ,T0.DocEntry
                ")
            ->leftJoin('M_Jetty AS T3', 'T3.DocEntry', 'T0.Jetty')
            ->where('T0.VesselName', '=', $request->vesselName)
            ->whereRaw("convert(varchar, T0.VesselDeparture, 106) = '{$request->voyage}' ")
            ->whereRaw("T0.DocEntry NOT IN (SELECT LocalID FROM BHLOCAL where Status <> 'Cancel')")
            ->where('T0.VesselType', '=', $request->vesselType)
            ->where('T0.TransType', '=', $request->type)
            ->first();

        if ($query_header) {
            $filed_jetty = $query_header->Jetty;
            $port = $query_header->Port;
            $vessel_arrive = $query_header->VesselDeparture;

            $query = DB::table('T_MDOC AS T0')
                ->leftJoin('M_TBC AS T1', 'T1.DocEntry', 'T0.BC_type_key')
                ->leftJoin('M_Tenant AS T2', 'T2.DocEntry', 'T0.Tenant_key')
                ->leftJoin('users AS T3', 'T3.id', 'T0.Created_id')
                ->leftJoin('M_BP AS T4', 'T4.DocEntry', 'T0.BP')
                ->leftJoin('M_Agent AS T5', 'T5.DocEntry', 'T0.Agent')
                ->selectRaw("
                    T0.*
                    ,T2.Name AS Tenant
                    ,T2.FullName AS TenantFullName
                    ,T4.Name AS BP
                    ,T5.Name AS Agent
                    ,T0.No_bl AS NoBL
                    ,T1.Type AS BCType
                    ,CONVERT(VARCHAR, T0.Date_bl, 105) AS DateBL
                    ,T0.AJU_No
                    ,T0.ItemName AS LoadingItem
                    ,T0.UnitWeight
                    ,T0.REG_No
                    ,T0.SPPB_no AS SPE_No
                    ,CASE
                        WHEN UPPER(T0.UnitWeight) = 'KGS'  THEN ROUND(T0.ItemQty / 1000, 5)
                        WHEN UPPER(T0.UnitWeight) = 'KG' THEN ROUND(T0.ItemQty / 1000, 5)
                        ELSE ROUND(T0.ItemQty, 5)
                    END as LoadingQty
                    ,(
                        SELECT COUNT(*) FROM BEXP1 as x
                        WHERE x.ExportId = T0.DocEntry AND x.Type='Import'
                    )AS CountDetails
                    ,(
                        SELECT TOP 1 BilledBy FROM MPLTENANT WHERE TenantKey = T0.Tenant_key
                    ) AS CompanyHeader
                    , 'Yes' AS LoadingUnloadingType
                    , 'B/L Weight' AS WeightCategory
                    ,CONVERT(VARCHAR, T0.REG_date, 105) AS REG_date
                    ,CONVERT(VARCHAR, T0.Ebilling_date, 105) AS Ebilling_date
                    ,'attachment' AS Attachment
                    ,CONVERT(VARCHAR, T0.SPPB_date, 105) AS SPE_date
                    ,T0.Created_by
                    ,NULL AS Notification
                    ,T0.Remarks
                    ,0 AS CountDetails
                    ,T0.Status
                    ,'${filed_jetty}' AS Jetty
                    ,'${port}' AS Port
                    ,T0.DocNum
                    ,T0.DocEntry As BaseId
                    ,NULL AS DocEntry
                    ,CASE
                        WHEN (SELECT COUNT(*) FROM MPLSL
                                WHERE TenantKey = T0.Tenant_key AND Type ='Tenant'
                                AND '${vessel_arrive}' >= PeriodStart AND '${vessel_arrive}' <= PeriodEnd) > 0
                            THEN (
                                SELECT Top 1 Price
                                FROM MPLSL
                                WHERE TenantKey=T0.Tenant_key AND Type ='Tenant'
                                AND '${vessel_arrive}' >= PeriodStart AND '${vessel_arrive}' <= PeriodEnd
                            )
                        ELSE
                            (
                                SELECT Top 1 Price
                                FROM MPLSL
                                WHERE Type ='Global'
                                AND '${vessel_arrive}' >= PeriodStart AND '${vessel_arrive}' <= PeriodEnd
                            )
                    END AS ServiceLoading
                    ,CASE
                        WHEN (SELECT COUNT(*) FROM MPLPS
                                WHERE TenantKey = T0.Tenant_key AND Type ='Tenant'
                                AND '${vessel_arrive}' >= PeriodStart AND '${vessel_arrive}' <= PeriodEnd
                                ) > 0
                            THEN (
                                SELECT Top 1 Price
                                FROM MPLPS
                                WHERE TenantKey=T0.Tenant_key AND Type ='Tenant'
                                AND '${vessel_arrive}' >= PeriodStart AND '${vessel_arrive}' <= PeriodEnd

                            )
                        ELSE
                            0
                    END AS Price
                    ,CASE
                        WHEN (SELECT COUNT(*) FROM MPLSL
                                WHERE TenantKey = T0.Tenant_key AND Type ='Tenant'
                                AND '${vessel_arrive}' >= PeriodStart AND '${vessel_arrive}' <= PeriodEnd) > 0
                            THEN (
                                SELECT Top 1 B.Currency
                                FROM MPLSL AS A
                                LEFT JOIN M_Currency AS B ON A.Currency = B.DocEntry
                                WHERE A.TenantKey=T0.Tenant_key AND A.Type ='Tenant'
                                AND '${vessel_arrive}' >= A.PeriodStart AND '${vessel_arrive}' <= A.PeriodEnd
                            )
                        ELSE
                            (
                                SELECT Top 1 B.Currency
                                FROM MPLSL AS A
                                LEFT JOIN M_Currency AS B ON A.Currency = B.DocEntry
                                WHERE  A.Type ='Global'
                                AND '${vessel_arrive}' >= A.PeriodStart AND '${vessel_arrive}' <= A.PeriodEnd
                            )
                    END AS CurrencyServiceLoading
                    ,CASE
                        WHEN (SELECT COUNT(*) FROM MPLPS
                                WHERE TenantKey = T0.Tenant_key AND Type ='Tenant'
                                AND '${vessel_arrive}' >= PeriodStart AND '${vessel_arrive}' <= PeriodEnd
                                ) > 0
                            THEN (
                                SELECT Top 1 B.Currency
                                FROM MPLPS AS A
                                LEFT JOIN M_Currency AS B ON A.Currency = B.DocEntry
                                WHERE A.TenantKey=T0.Tenant_key AND A.Type ='Tenant'
                                AND '${vessel_arrive}' >= A.PeriodStart AND '${vessel_arrive}' <= A.PeriodEnd

                            )
                        ELSE
                            ''
                    END AS CurrencyPortService
                    ,CASE
                        WHEN (SELECT COUNT(*) FROM MPLPS
                                WHERE TenantKey = T0.Tenant_key AND Type ='Tenant'
                                AND '${vessel_arrive}' >= PeriodStart AND '${vessel_arrive}' <= PeriodEnd
                                ) > 0
                            THEN (
                                SELECT Top 1 CONCAT(B.ItemCode, ' - ', B.FrgnName)
                                FROM MPLPS AS A
                                LEFT JOIN MPS AS B ON B.DocEntry = A.PSKey
                                WHERE A.TenantKey=T0.Tenant_key AND A.Type ='Tenant'
                                AND '${vessel_arrive}' >= A.PeriodStart AND '${vessel_arrive}' <= A.PeriodEnd

                            )
                        ELSE
                            ''
                    END AS PortServiceType
                    --,T0.DocStatus
                    , 'Guo Yao Min' AS Signature1
                ")
                ->where('T0.DocNum', '=', $query_header->ExportId)
                ->where('T0.Deleted', '=', 'N')
                ->where('T0.DocType', '=', 'Local')
                ->get();

            return response()->json([
                'header' => $query_header,
                'rows' => $query,
            ]);
        }
    }

    /**
     * @return \Illuminate\Http\JsonResponse
     */
    public function getPortService(Request $request)
    {
        return $this->dataPortService($request);
    }

    /**
     * @param $docNum
     *
     * @return int
     */
    public function checkDoubleDoc($docNum)
    {
        return DB::table('BHLOCAL')
            ->where('LocalID', '=', $docNum)
            ->where('Status', '=', 'Open')
            ->count();
    }

    /**
     * @return \Illuminate\Http\JsonResponse
     */
    public function fetchDocNum()
    {
        $doc_num = DB::table('BHLOCAL')->select('DocNum')
            ->orderBY('DocNum', 'DESC')
            ->first();

        $document = $doc_num ? (int) $doc_num->DocNum + 1 : $this->generateDocNum(Carbon::now());

        return response()->json([
            'DocNum' => $document,
        ]);
    }

    /**
     * @param $sysDate
     *
     * @return string
     */
    public function generateDocNum($sysDate)
    {
        $data_date = strtotime($sysDate);
        $year_val = date('y', $data_date);
        $full_year = date('Y', $data_date);
        $month = date('m', $data_date);
        $day_val = date('j', $data_date);
        $end_date = date('t', $data_date);

        if ($day_val == 1) {
            $doc_num = DB::table('BHLOCAL')
                ->selectRaw('ISNULL(DocNum, 0) as DocNum')
                ->whereBetween(DB::raw('convert(varchar, created_at, 23)'), ["${full_year}-${month}-01", "${full_year}-${month}-${end_date}"])
                ->orderBy('created_at', 'DESC');
            if ($doc_num->count() > 0) {
                $doc_num = $doc_num->first();
                $number = !$doc_num ? '00000000' : $doc_num->DocNum;
                $clear_doc_num = (int) substr($number, 4, 7);
                $number = $clear_doc_num + 1;
                return $year_val . $month . sprintf('%04s', $number);
            }

            return $year_val . $month . sprintf('%04s', '1');
        }
        $doc_num = DB::table('BHLOCAL')
            ->selectRaw('ISNULL(DocNum, 0) as DocNum')
            ->whereBetween(DB::raw('convert(varchar, created_at, 23)'), ["${full_year}-${month}-01", "${full_year}-${month}-${end_date}"])
            ->orderBy('created_at', 'DESC')
            ->first();

        $number = !$doc_num ? '00000000' : $doc_num->DocNum;
        $clear_doc_num = (int) substr($number, 4, 7);
        $number = $clear_doc_num + 1;
        return $year_val . $month . sprintf('%04s', $number);
    }

    public function checkDocNum()
    {
    }

    /**
     * @param Request $request
     * @param $id
     *
     * @return \Illuminate\Http\JsonResponse
     *
     * @throws \Exception
     */
    public function store(Request $request, $id)
    {
        DB::beginTransaction();
        try {
            if (empty($request->form['PortService']) || empty($request->form['Jetty'])) {
                return response()->json([
                    'errors' => true,
                    'message' => 'Port Service and Jetty cannot empty!',
                ], 422);
            }

            if ($id == 'null') {
                $document_num = DB::table('BHLOCAL')
                    ->where('DocNum', '=', $request->form['DocNum'])
                    ->first();
            }

            if (empty($request->form['VesselDeparture'])) {
                return response()->json([
                    'errors' => true,
                    'message' => 'Vessel Departure cannot empty!',
                ], 422);
            }

            if (empty($request->form['PeriodDate'])) {
                return response()->json([
                    'errors' => true,
                    'message' => 'Period Date Cannot Empty!',
                ], 422);
            }

            if (empty($request->form['BillingNoteDate'])) {
                return response()->json([
                    'errors' => true,
                    'message' => 'Billing Note Date Cannot Empty!',
                ], 422);
            }

            if (array_key_exists('ExportId', $request->form)) {
                if ($request->form['ExportId']) {
                    if ($this->checkDoubleDoc($request->form['ExportId']) > 0) {
                        DB::table('BHLOCAL')
                            ->whereRaw('
                                (
                                    SELECT COUNT(*) FROM BEXP WHERE DocNum = BHLOCAL.DocEntry
                                ) = 0
                            ')
                            ->where('Status', '=', 'Open')
                            ->delete();

                        // return response()->json([
                        //    "errors" => true,
                        //    "message" => "Double input for this data export! Please use other data"
                        // ]);
                    }
                }
            }

            $details = collect($request->details);

            $vessel_type = ['lCT', 'TB', 'SPB', 'KM'];

            if ($details) {
                $all_fill = [];
                foreach ($details as $index_bl => $items) {
                    if (!array_key_exists('CompanyHeader', $items)) {
                        $lines = $index_bl + 1;
                        return response()->json([
                            'errors' => true,
                            'message' => "Line ${lines}: Company header cannot empty!",
                        ], 422);
                    }

                    if (!array_key_exists('Classification', $items)) {
                        $lines = $index_bl + 1;
                        return response()->json([
                            'errors' => true,
                            'message' => "Line ${lines}: Classifications cannot empty!",
                        ], 422);
                    }

                    if (empty($items['Classification'])) {
                        $lines = $index_bl + 1;
                        return response()->json([
                            'errors' => true,
                            'message' => "Line ${lines}: Classification cannot empty!",
                        ], 422);
                    }

                    if (empty($items['CompanyHeader'])) {
                        $lines = $index_bl + 1;
                        return response()->json([
                            'errors' => true,
                            'message' => "Line ${lines}: Company header cannot empty!",
                        ], 422);
                    }

                    if (empty($items['Agent'])) {
                        $lines = $index_bl + 1;
                        return response()->json([
                            'errors' => true,
                            'message' => "Line ${lines}: Agent cannot empty!",
                        ], 422);
                    }

                    if (empty($items['Tenant'])) {
                        $lines = $index_bl + 1;
                        return response()->json([
                            'errors' => true,
                            'message' => "Line ${lines}: Tenant cannot empty!",
                        ], 422);
                    }

                    $tenant = $this->getDataTenantByName($items['Tenant']);

                    if ($tenant->IsTenant == 'Tenant') {
                        $this->validatePostingPeriod($request->form['PeriodDate']);
                    } else {
                        $this->validatePostingPeriod($request->form['BillingNoteDate']);
                    }

                    if (!array_key_exists('Tenant', $items)) {
                        $lines = $index_bl + 1;
                        return response()->json([
                            'errors' => true,
                            'message' => "Line ${lines}: Tenant cannot empty!",
                        ], 422);
                    }

                    if (in_array($request->form['VesselType'], $vessel_type)) {
                        if (!array_key_exists('QtyEstimate', $items)) {
                            $lines = $index_bl + 1;
                            return response()->json([
                                'errors' => true,
                                'message' => "Line ${lines}: Qty Estimate cannot empty!",
                            ], 422);
                        } elseif (empty($items['QtyEstimate'])) {
                            $lines = $index_bl + 1;
                            return response()->json([
                                'errors' => true,
                                'message' => "Line ${lines}: Qty Estimate cannot empty!",
                            ], 422);
                        }

                        if (!array_key_exists('ChargeTo', $items)) {
                            $lines = $index_bl + 1;
                            return response()->json([
                                'errors' => true,
                                'message' => "Line ${lines}: ChargeTo cannot empty!",
                            ], 422);
                        } elseif (empty($items['ChargeTo'])) {
                            $lines = $index_bl + 1;
                            return response()->json([
                                'errors' => true,
                                'message' => "Line ${lines}: ChargeTo cannot empty!",
                            ], 422);
                        }


                        if (floatval($items['TotalEstimate']) == 0) {
                            $lines = $index_bl + 1;
                            return response()->json([
                                'errors' => true,
                                'message' => "Line ${lines}: Total Estimate cannot empty!",
                            ], 422);
                        }

                        if (!array_key_exists('TaxCode', $items)) {
                            $lines = $index_bl + 1;
                            return response()->json([
                                'errors' => true,
                                'message' => "Line ${lines}: TaxCodes cannot empty!",
                            ], 422);
                        } elseif (!isset($items['TaxCode'])) {
                            $lines = $index_bl + 1;
                            return response()->json([
                                'errors' => true,
                                'message' => "Line ${lines}: TaxCode cannot empty!",
                            ], 422);
                        }
                    }
                } // Details
            }

            // get header
            $header = $this->getHeaderDoc($id, $request);
            // set created at
            $created = !empty($header) ? $header->created_at : Carbon::now();
            // process header
            $doc_num = $this->processHeaderDoc($header, $created, $request);

            if ($doc_num) {
                if ($details) {
                    $all_fill = [];
                    foreach ($details as $index_bl => $items) {
                        if (!array_key_exists('CompanyHeader', $items)) {
                            $lines = $index_bl + 1;
                            return response()->json([
                                'errors' => true,
                                'message' => "Line ${lines}: Company header cannot empty!",
                            ], 422);
                        }

                        if (!array_key_exists('Classification', $items)) {
                            $lines = $index_bl + 1;
                            return response()->json([
                                'errors' => true,
                                'message' => "Line ${lines}: Classifications cannot empty!",
                            ], 422);
                        }

                        if (empty($items['Classification'])) {
                            $lines = $index_bl + 1;
                            return response()->json([
                                'errors' => true,
                                'message' => "Line ${lines}: Classification cannot empty!",
                            ], 422);
                        }

                        $last_data = key_exists('DocEntry', $items) ? $items['DocEntry'] : null;
                        $index = $doc_num;
                        $port_service = array_key_exists('PortServiceType', $items) ?
                            substr($items['PortServiceType'], 0, 7) :
                            substr($request->form['PortService'], 0, 7);
                        $monitor_doc = BEXP::where('DocEntry', '=', $last_data)->first();
                        $check_ps = $this->checkPortSeviceExist(
                            $port_service,
                            $request->form['VesselArrival'],
                            $items['Tenant']
                        );

                        $check_sl = $this->checkServiceLoadingExist(
                            $request->form['VesselArrival'],
                            $items['Tenant']
                        );

                        if ($check_ps == 'tenant') {
                            return response()->json([
                                'errors' => true,
                                'message' => "Please Define Price List Port Service
                                ${port_service} For Tenant " . $items['Tenant'] . ' In Range Lower than
                                and greater than ' . $request->form['VesselArrival'],
                            ], 422);
                        }
                        if ($check_ps == 'global') {
                            return response()->json([
                                'errors' => true,
                                'message' => "Please Define Price List For Port Service ${port_service}
                                In Range Lower than and greater than " . $request->form['VesselArrival'],
                            ], 422);
                        }
                        if ($check_sl == 'tenant') {
                            return response()->json([
                                'errors' => true,
                                'message' => 'Please Define Price List Service Loading For
                                    Tenant ' . $items['Tenant'] . ' In Range Lower than and greater than ' .
                                    $request->form['VesselArrival'],
                            ], 422);
                        }
                        if ($check_sl == 'global') {
                            return response()->json([
                                'errors' => true,
                                'message' => 'Please Define Price List For Service Loading
                                        In Range Lower than and greater than ' . $request->form['VesselArrival'],
                            ], 422);
                        }
                        // $doc_entry = $this->saveData($index_bl, $items, $request, $doc_num);
                        $doc_entry = $this->storeBilling(
                            $index_bl,
                            $items,
                            $request,
                            $doc_num,
                            $request->form['TransType']
                        );

                        if ($doc_entry) {
                            $all_fill[] = true;
                            //$this->reportBilling($request, $doc_entry);
                        } else {
                            $all_fill[] = true;
                        }
                    } // Details
                    if (!in_array(false, $all_fill)) {
                        DB::commit();
                        return response()->json([
                            'errors' => false,
                            'docEntry' => $doc_num,
                            'message' => $id != 'null' ? 'Data updated!' : 'Data inserted!',
                        ]);
                    }
                    return response()->json([
                        'errors' => true,
                        'message' => 'Failed process B/L data!',
                    ], 422);
                }
            }
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'errors' => true,
                'message' => $e->getMessage(),
                'trace' => $e->getTrace()
            ], 422);
        }
    }

    /**
     * @param $id
     * @param $request
     *
     * @return |null
     */
    public function getHeaderDoc($id, $request)
    {
        $header = null;
        if ($id != 'null') {
            $header = BHLOCAL::where('DocEntry', '=', $id)->first();
        }

        return $header;
    }

    /**
     * @param $jetty
     *
     * @return mixed
     */
    public function getJettyByName($jetty)
    {
        if ($jetty) {
            $data_jetty = DB::table('M_Jetty')->where('Name', '=', $jetty)->first();
            return $data_jetty->DocEntry;
        }
    }

    /**
     * @param $i
     * @param $items
     * @param $request
     * @param $id
     *
     * @return mixed
     */
    public function saveData($i, $items, $request, $id)
    {
    }

    /**
     * @param Request $request
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function exportDetails(Request $request)
    {
        return $this->billingDetails($request, 'Import');
    }

    /**
     * @param Request $request
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function storeExportDetails(Request $request)
    {
        DB::beginTransaction();
        try {
            $details = collect($request->details);
            if ($details) {
                $total_qty = 0;
                foreach ($details as $detail) {
                    if (empty($detail['LoadingQty'])) {
                        return response()->json([
                            'errors' => true,
                            'message' => 'Weight cannot Empty!',
                        ], 422);
                    }

                    $total_qty += $detail['LoadingQty'];
                }
                //check if total input > base qty
                if ($total_qty != $request->dataExport['LoadingQty']) {
                    return response()->json([
                        'errors' => true,
                        'message' => 'Total Qty not equal to base Qty!',
                    ], 422);
                }
                foreach ($details as $detail) {
                    //if ($request->dataExport["LoadingQty"])
                    if (empty($detail['Tenant'])) {
                        return response()->json([
                            'errors' => true,
                            'message' => 'Tenant cannot Empty!',
                        ], 422);
                    }

                    $header = BEXP1::where('DocEntry', '=', $detail['DocEntry'])
                        ->first();
                    $port_service = array_key_exists('PortServiceType', $detail) ?
                        substr($detail['PortServiceType'], 0, 7) :
                        substr($request->form['PortService'], 0, 7);

                    $check_ps = $this->checkPortSeviceExist(
                        $port_service,
                        $request->form['VesselArrival'],
                        $detail['Tenant']
                    );

                    $check_sl = $this->checkServiceLoadingExist(
                        $request->form['VesselArrival'],
                        $detail['Tenant']
                    );

                    if ($check_ps == 'tenant') {
                        return response()->json([
                            'errors' => true,
                            'message' => "Please Specify Price List For ${port_service} AND Tenant " . $detail['Tenant'],
                        ], 422);
                    }
                    if ($check_ps == 'global') {
                        return response()->json([
                            'errors' => true,
                            'message' => "Please Specify Price List For ${port_service}",
                        ], 422);
                    }
                    if ($check_sl == 'tenant') {
                        return response()->json([
                            'errors' => true,
                            'message' => 'Please Specify Price List For Service Loading AND Tenant ' . $detail['Tenant'],
                        ], 422);
                    }
                    if ($check_sl == 'global') {
                        return response()->json([
                            'errors' => true,
                            'message' => 'Please Specify Price List For Service Loading',
                        ], 422);
                    }
                    $price = $this->getPrice(
                        $port_service,
                        $request->form['VesselArrival'],
                        $detail['Tenant'],
                        $request->type
                    );
                    $doc_num = $this->storeBillingDetail(
                        $request,
                        $request->type,
                        $price,
                        $header,
                        $detail,
                        $port_service
                    );
                }
                if ($doc_num) {
                    return response()->json([
                        'errors' => false,
                        'message' => 'Data saved!',
                        'total' => DB::table('BEXP1')->where('ExportId', '=', $request->exportId)->count(),
                    ]);
                }
            }
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'errors' => true,
                'trace' => $e->getTrace(),
                'message' => $e->getMessage(),
            ], 422);
        }
    }

    public function removeExportDetails(Request $request)
    {
    }

    /**
     * @param $request
     * @param $doc_num
     *
     * @return \Illuminate\Http\JsonResponse
     *
     * @throws \Exception
     */
    public function reportBilling(Request $request)
    {
        $response = $this->dataReportBilling($request, $request->type);
        // return redirect($res);
        return response()->download($response);
    }

    /**
     * @return \Illuminate\Http\JsonResponse
     */
    public function loadingUnLoading()
    {
        $array_data = [
            'All',
            'Port Service',
            'Service Loading',
        ];

        return response()->json([
            'rows' => $array_data,
        ]);
    }

    public function cancelDoc(Request $request, $doc_entry)
    {
        try {
            $header = BHLOCAL::where('DocEntry', '=', $doc_entry)
                ->first();

            $header->Status = 'Cancel';
            $header->UpdatedBy = $request->user()->id;
            $header->updated_at = Carbon::now();
            $header->save();

            DB::table("BEXP")
                ->where("DocNum", "=", $doc_entry)
                ->whereIn("Type", ["IN", "OUT"])
                ->update([
                    "Status" => "Cancel"
                ]);

            return response()->json([
                'errors' => false,
                'docEntry' => $doc_entry,
                'message' => 'Document cancelled',
            ]);
        } catch (\Exception $exception) {
            return response()->json([
                'errors' => true,
                'message' => $exception->getMessage(),
            ]);
        }
    }

    /**
     * @param $header
     * @param $created
     * @param $request
     *
     * @return mixed
     */
    protected function processHeaderDoc($header, $created, $request)
    {
        if ($header) {
            $header->PortService = $request->form['PortService'];
            $header->PeriodDate = $request->form['PeriodDate'] ?
                date('Y-m', strtotime($request->form['PeriodDate'])) . '-01' : null;
            //$header->Status = $request->form['Status'];
            $header->Jetty = $this->getJettyByName($request->form['Jetty']);
            $header->updated_at = Carbon::now();
            $header->Remarks = (!empty($request->form['Remarks'])) ? $request->form['Remarks'] : '';
            $header->BillingNoteDate = $request->form['BillingNoteDate'] ? date('Y-m-d', strtotime($request->form['BillingNoteDate'])) : null;
            $header->Type = $request->form['TransType'];
            $header->created_at = $created;
            $header->UpdatedBy = $request->user()->id;
            $header->save();
            $doc_num = $header->DocEntry;
        } else {
            $header = new BHLOCAL();
            $header->DocNum = $this->generateDocNum(Carbon::now());
            $header->PostingDate = date('Y-m-d');
            $header->PortService = $request->form['PortService'];
            $header->BillingNoteDate = $request->form['BillingNoteDate'] ? date('Y-m-d', strtotime($request->form['BillingNoteDate'])) : null;
            $header->PeriodDate = $request->form['PeriodDate'] ?
                date('Y-m', strtotime($request->form['PeriodDate'])) . '-01' : null;
            $header->Status = 'Open';
            $header->YearArrival = date('Y');
            $header->Jetty = $this->getJettyByName($request->form['Jetty']);
            $header->LocalID = $request->form['ExportId'];
            $header->Type = $request->form['TransType'];
            $header->Remarks = array_key_exists('Remarks', $request->form) ? ((!empty($request->form['Remarks'])) ? $request->form['Remarks'] : '') : '';
            $header->created_at = Carbon::now();
            $header->CreatedBy = $request->user()->id;
            $header->UpdatedBy = 0;
            $header->save();
            $doc_num = $header->DocEntry;
        }
        //        DB::table("L_Master")
        //            ->where("DocEntry", "=", $request->form['ExportId'])
        //            ->update([
        //                "VesselDeparture" => $request->form['VesselDeparture']
        //            ]);
        return $doc_num;
    }
    /**
     * Calculates the estimate.
     *
     * @param      \Illuminate\Http\Request  $request  The request
     *
     */
    public function calculateEstimate(Request $request)
    {
        $qty = floatval($request->qtyEstimate);
        $qty_revised = floatval($request->qtyRevised);
        $price = floatval($request->priceEstimate);
        $price_revised = floatval($request->priceRevised);
        $tax_code = $request->taxCode;
        $departure = $request->vesselDeparture;

        if ($departure) {
            $departure = date('Y-m-d', strtotime($departure));
            $rate = ExchangeRate::where('RateDate', '=', $departure)->first();
            if ($rate) {
                $total = round(round($qty * $price, 2) * $rate->IdrPrice);
                $total_with_tax = floor($tax_code * $total / 100);

                $total_revised = $qty_revised * ($price_revised * $rate->IdrPrice);
                $total_with_tax_revised = $tax_code * $total_revised / 100;

                return response()->json([
                    'total' => ($total + $total_with_tax),
                    'total_revised' => ($total_revised + $total_with_tax_revised),
                ]);
            } else {
                $idrPrice = $this->getLatestBiRate("USD", $departure);
                $exhangeRate = ExchangeRate::where("RateDate", "=", $departure)->first();
                if ($exhangeRate) {
                    if (empty($exhangeRate->IdrPriceBi)) {
                        $exhangeRate->update([
                            "IdrPriceBi" => $idrPrice
                        ]);
                    }
                } else {
                    ExchangeRate::create([
                        'RateDate' => $departure,
                        'IdrPrice' => $idrPrice,
                        'IdrPriceBi' => $idrPrice,
                        'CreatedBy' => $request->user()->id,
                        'created_at' => Carbon::now(),
                    ]);
                }

                if (!$exhangeRate) {
                    return response()->json([
                        'error' => true,
                        'idrPrice' => $idrPrice,
                        'message' => 'Rate for date ' . $departure . ' is not set!'
                    ], 422);
                }
            }
        } else {
            return response()->json([
                'error' => true,
                'message' => 'Billing Note periode cannot empty!'
            ], 422);
        }
    }
}
